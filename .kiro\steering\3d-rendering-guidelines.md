# 3D Rendering Guidelines

## Model Loading Best Practices

- Use `useOptimizedModelLoader` or `useModelLoader` hooks
- Models are hosted on AWS S3 with DRACO compression
- Adult models: `https://upod.s3.eu-central-1.amazonaws.com/treatmentsV2/`
- Children models: `https://upod.s3.eu-central-1.amazonaws.com/treatmentsV3/child/`
- Always clone models (`model.clone()`) for multiple instances
- Implement proper model caching to avoid redundant loads

## Treatment Visualization System

### Multiple Treatment Layering

When multiple treatments exist on same tooth:

1. Sort treatments by `created_at` (newest first)
2. Apply transparency based on treatment age:
   - Newest treatment: 0.8 opacity
   - Each older treatment: reduce opacity by 0.2
   - Minimum opacity: 0.15
3. Use `applyAntiZFightingProperties()` from `materialUtils.js`

### Z-Fighting Prevention

```javascript
// Apply to treatment materials
material.polygonOffset = true;
material.polygonOffsetFactor = index * 1.0;
material.polygonOffsetUnits = index * 1.0;
material.renderOrder = -index;
material.depthWrite = index === 0; // Only newest writes depth
```

## Healthy Tooth Visibility Logic

**IMPORTANT**: Current implementation gap exists

- `treatmentHasRoots()` function exists but not used
- `shouldShowHealthyTooth()` doesn't check for treatment roots
- **Required Logic**: If treatments have roots, hide healthy tooth; if no roots, show healthy tooth

## Mouse Interactions

- Use `useMouseInteractions` hook for raycasting
- Implement hover states for tooth highlighting
- Support surface-level selection for precise treatment application
- Handle right-click context menus via `openRightClickModal`

## Materials and Transparency

- Define materials in `/src/constants/materials.js`
- Use `transparencyUtils.js` for skull/jaw transparency
- Implement proper render order for transparent objects
- Handle transparency for overlapping treatments

## Animation System

- Jaw animations controlled via `useAnimationControls`
- Support jaw opening/closing animations
- Link teeth animations to jaw movements
- Provide UI controls via `AnimationControls` component
