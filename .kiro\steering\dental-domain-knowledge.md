# Dental Domain Knowledge

## Tooth Numbering System

- Adult teeth: 32 teeth numbered 1-32
- Children teeth: Different numbering system
- Position format: "UL8" (Upper Left 8), "LR3" (Lower Right 3), etc.
- Support both ADULT and CHILDREN patient types

## Treatment Types

### Full Tooth Treatments

- **Bridge**: Connects multiple teeth, requires bridge_treatment flag
- **Crown**: Covers entire tooth
- **Implant**: Replaces missing tooth
- **Extraction**: Removes tooth, may set remove_tooth_when_completed
- **Root Canal**: Internal tooth treatment
- **BoneGraft**: Bone reconstruction
- **Apictomy**: Root tip surgery

### Surface-Based Treatments

- **Filling**: Applied to specific tooth surfaces
- **Decay**: Indicates damaged areas
- **Sealant**: Protective coating
- **Surfaces**: Mesial, Distal, Occlusal, Buccal, Lingual, etc.

### Special Indicators

- **Missing Tooth**: Use missing_tooth_indicator flag
- **Mixed Dentition**: Combination of adult/child teeth
- **Watched Tooth**: Monitored for changes

## Treatment Properties

```javascript
{
  id: "unique_id",
  ctid: "clinical_treatment_id",
  name: "<PERSON><PERSON><PERSON>",
  full_tooth_treatment: boolean,
  patient_treatment: boolean,
  remove_tooth_when_completed: boolean,
  remove_treatment_when_completed: boolean,
  bridge_treatment: boolean,
  missing_tooth_indicator: boolean,
  mixed_dentition: boolean,
  need_default_tooth: boolean,
  default_tooth_transparent: boolean,
  created_at: "ISO_date",
  completed_at: "ISO_date_or_null",
  surfaces: { "SurfaceName": { decaySeverity: 1, fillingSize: 1 } }
}
```

## Dental Views

- **Skull View**: Full skull with transparent bone, shows tooth roots
- **Jaw View**: Upper/lower jaw only, supports animations
- **Charting View**: Traditional 2D dental chart interface
- **Single Treatment View**: Detailed view of individual tooth with treatments
