# Development Standards

## Code Style & Structure

- Use functional components with hooks (no class components)
- Follow React 19 patterns and best practices
- Use JSX file extension for React components
- Implement proper error boundaries for 3D rendering components
- Use ESLint configuration as defined in `eslint.config.js`

## Component Organization

- **UI Components**: Place in `/src/components/UI/` (buttons, modals, controls)
- **View Components**: Place in `/src/components/views/` (skull, jaw, charting, single_treatment)
- **Scene Control**: Place in `/src/components/scene_control/` (camera, lights, interactions)
- **Custom Hooks**: Place in `/src/hooks/` with `use` prefix
- **Utilities**: Place in `/src/utils/` for general functions
- **Helpers**: Place in `/src/helpers/` for view-specific logic

## State Management

- Use `TeethContext` for global dental data state
- Access context via `useTeeth()` hook
- Avoid prop drilling - use context for shared state
- Keep component-specific state local when possible

## Performance Guidelines

- Use model caching via `useOptimizedModelLoader` or `useModelLoader`
- Clone models (`model.clone()`) instead of reloading
- Minimize material property changes at runtime
- Implement proper cleanup in useEffect hooks
- Use React.memo for expensive components when appropriate

## 3D Development

- Use React Three Fiber patterns, not vanilla Three.js
- Handle GLTF/GLB models with DRACO compression
- Implement proper raycasting for mouse interactions
- Use materials defined in `/src/constants/materials.js`
- Follow transparency and Z-fighting prevention patterns
