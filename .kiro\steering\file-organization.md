# File Organization and Patterns

## Directory Structure Standards

```
src/
├── components/
│   ├── UI/                    # Reusable UI components
│   ├── views/                 # Main view components
│   └── scene_control/         # 3D scene management
├── context/                   # React Context providers
├── hooks/                     # Custom React hooks
├── utils/                     # General utility functions
├── helpers/                   # View-specific helper functions
├── constants/                 # Configuration and constants
├── apis/                      # API communication (minimal)
├── assets/                    # Static assets
└── data/                      # Static data files
```

## Naming Conventions

### Files

- Components: `PascalCase.jsx` (e.g., `ViewSwitcher.jsx`)
- Hooks: `useCamelCase.js` (e.g., `useModelLoader.js`)
- Utils: `camelCase.js` (e.g., `modelUtils.js`)
- Constants: `camelCase.js` (e.g., `materials.js`)

### Variables and Functions

- React components: `PascalCase`
- Functions: `camelCase`
- Constants: `UPPER_SNAKE_CASE`
- Context values: `camelCase`

## Common Import Patterns

```javascript
// React imports first
import { useState, useEffect, useRef } from "react";

// Third-party imports
import { Canvas } from "@react-three/fiber";
import { OrbitControls } from "@react-three/drei";

// Local imports (grouped by type)
import { useTeeth } from "../context/TeethContext";
import { useModelLoader } from "../hooks/useModelLoader";
import { applyAntiZFightingProperties } from "../utils/materialUtils";
import { ADULT_MODELS_BASE_URL } from "../constants/models";
```

## Component Structure Template

```javascript
import { useState, useEffect } from "react";
import { useTeeth } from "../context/TeethContext";

const ComponentName = ({ prop1, prop2 }) => {
  // State declarations
  const [localState, setLocalState] = useState(null);

  // Context and hooks
  const { patientTeeth, setPatientTeeth } = useTeeth();

  // Effects
  useEffect(() => {
    // Effect logic
    return () => {
      // Cleanup
    };
  }, [dependencies]);

  // Event handlers
  const handleEvent = (data) => {
    // Handler logic
  };

  // Render
  return <div className="component-name">{/* JSX content */}</div>;
};

export default ComponentName;
```

## Key File References

- **Entry Point**: `src/main.jsx`
- **App Wrapper**: `src/AppWrapper.jsx` (provides TeethContext)
- **Main App**: `src/App.jsx` (view routing and state)
- **Global Context**: `src/context/TeethContext.jsx`
- **Message Handling**: `src/hooks/useTeethMessages.js`
- **Model Loading**: `src/hooks/useModelLoader.js`
- **3D Scene**: `src/components/scene.jsx`
