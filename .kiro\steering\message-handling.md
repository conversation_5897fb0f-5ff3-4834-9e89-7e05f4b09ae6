# Message Handling System

## Communication Pattern

- Application runs in iframe and communicates with parent via postMessage API
- Use `useTeethMessages()` hook for message handling
- All external communication goes through `teethMessageHandler.js`

## Incoming Messages (Parent → Iframe)

```javascript
// Initialize patient teeth data
{ type: "initialize_teeth", teeth: [...] }

// Clear all teeth data
{ type: "clear_teeth" }

// Select treatment for application
{ type: "treatment_selected", treatment: {...} }

// Treatment saved/deleted/completed
{ type: "charted_treatment_saved", ctId: "123" }
{ type: "charted_treatment_deleted", ctId: "123" }
{ type: "charted_treatment_completed", ctId: "123", position: "UL8" }

// Eraser tool state
{ type: "eraser_state_change", active: true }

// Tooth operations
{ type: "tooth_removed", position: "UL8" }
{ type: "watch_tooth", position: "UL8", watched: true }
{ type: "mixed_dentition_saved", position: "UL8", active: true }

// Single tooth initialization
{ type: "initialize_single_tooth", tooth: {...} }
```

## Outgoing Messages (Iframe → Parent)

```javascript
// Confirmation messages
teethMessages.teethInitialized();
teethMessages.teethCleared();
teethMessages.toothRendered();

// User interactions
teethMessages.selectPositionAndSurfaces("UL8", ["Mesial", "Distal"]);
teethMessages.selectTreatment({ id: 123, name: "Filling" });
teethMessages.removeTreatment("123");
teethMessages.removeTooth("UL8");

// Treatment operations
teethMessages.completeTreatment("123", "UL8");
teethMessages.addAnnotations("Note text", "123");
teethMessages.viewToothHistory("UL8");

// Special operations
teethMessages.setMixedDentition(true, "UL8");
teethMessages.watchTooth("UL8", true);
teethMessages.selectBridgeTreatmentPositions("UL2", "UL5");
```

## Message Handler Usage

```javascript
// In components
const teethMessages = useTeethMessages();

// Send message when user selects tooth
const handleToothClick = (position, surfaces) => {
  teethMessages.selectPositionAndSurfaces(position, surfaces);
};

// Handle incoming messages via TeethContext
const { patientTeeth } = useTeeth(); // Updates automatically from messages
```
