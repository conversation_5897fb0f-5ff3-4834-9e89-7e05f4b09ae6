# UPOD v2 Project Overview

## Project Description

UPOD v2 is a 3D dental visualization and treatment planning application built with React, Three.js, and React Three Fiber. It provides dentists and dental professionals with an interactive tool for visualizing dental conditions, planning treatments, and communicating with patients.

## Tech Stack

- **Frontend**: React 19.0.0
- **3D Graphics**: Three.js 0.161.0, React Three Fiber 9.0.4, Drei 10.0.0
- **Build Tool**: Vite 6.1.0 (dev server on port 3001)
- **State Management**: React Context API
- **Model Format**: GLTF/GLB with DRACO compression
- **Hosting**: AWS S3 for 3D models

## Key Features

- Multiple view modes: Skull, Jaw, Charting, Single Treatment
- Interactive 3D tooth selection and treatment application
- Animation system for jaw movements
- Screenshot functionality with S3 upload
- Message-based communication with parent iframe
- Treatment visibility management and layering
- Support for both adult and children dental models

## Architecture

- Entry point: `src/main.jsx` → `src/AppWrapper.jsx` → `src/App.jsx`
- Context-based state management via `TeethContext`
- Component organization: `/components/UI`, `/components/views`, `/components/scene_control`
- Utilities: `/utils`, `/helpers`, `/hooks`, `/constants`
- External communication via postMessage API for iframe integration
