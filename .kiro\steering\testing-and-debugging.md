# Testing and Debugging Guidelines

## Development Commands

```bash
# Start development server
npm run dev  # Runs on port 3001

# Build for production
npm run build

# Run linting
npm run lint

# Preview production build
npm run preview
```

## Testing Strategies

### Manual Testing

- Test all view modes: skull, jaw, charting, single_treatment
- Verify treatment application and visibility
- Test mouse interactions (hover, click, right-click)
- Validate message communication with parent iframe
- Check animation controls functionality
- Test screenshot capture and upload

### Browser Developer Tools

- Use React Developer Tools for component inspection
- Monitor Three.js performance via browser performance tab
- Check console for WebGL errors or warnings
- Inspect network requests for model loading issues
- Use memory profiler for 3D resource leaks

## Common Issues and Solutions

### Model Loading Problems

- **Issue**: Models not loading or appearing
- **Check**: Network tab for 404s, CORS issues, or S3 access
- **Solution**: Verify model URLs in `/src/constants/models.js`

### Performance Issues

- **Issue**: Slow rendering or frame drops
- **Check**: Too many models loaded simultaneously
- **Solution**: Implement proper model caching and cleanup

### Z-Fighting or Transparency Issues

- **Issue**: Treatments flickering or not visible
- **Check**: Material renderOrder and polygonOffset settings
- **Solution**: Use `applyAntiZFightingProperties()` correctly

### Message Communication Failures

- **Issue**: Parent-iframe communication not working
- **Check**: Console for postMessage errors
- **Solution**: Verify message format matches expected structure

## Debugging Tools

### React Three Fiber Debugging

```javascript
// Add to components for debugging
<primitive object={new THREE.AxesHelper(5)} />
<Stats /> // From @react-three/drei
```

### Console Logging Best Practices

```javascript
// Use structured logging
console.log("Model loaded:", { toothNumber, treatmentName, modelPath });

// Avoid logging in production
if (process.env.NODE_ENV === "development") {
  console.log("Debug info:", data);
}
```
