<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PostMessage Test</title>
  </head>
  <body>
    <h1>Parent Page</h1>
    <iframe id="app-iframe" src="/index.html" width="800" height="600"></iframe>
    <br />
    <button id="send-message-btn">Send Message to Iframe</button>
    <button id="initialize-teeth-btn">Initialize Teeth Data</button>
    <button id="clear-teeth-btn">Clear Teeth</button>
    <br /><br />
    <label for="treatment-select">Select Treatment:</label>
    <select id="treatment-select">
      <option value="">--Please choose a treatment--</option>
    </select>
    <br /><br />
    <button id="toggle-eraser-btn">Toggle Eraser (Active: false)</button>
    <button id="send-treatment-selected-btn">
      Send Treatment Selected Message
    </button>
    <div id="selection-info">
      <h3>All Treatments:</h3>
      <ul id="treatments-list"></ul>
    </div>

    <script type="module">
      // import { treatmentsData } from "../src/data/treatmentsData.js";

      let treatments = [];
      let isEraserActive = false;
      const iframe = document.getElementById("app-iframe");
      const sendMessageBtn = document.getElementById("send-message-btn");
      const initializeTeethBtn = document.getElementById(
        "initialize-teeth-btn",
      );
      const clearTeethBtn = document.getElementById("clear-teeth-btn");
      const toggleEraserBtn = document.getElementById("toggle-eraser-btn");
      const treatmentSelect = document.getElementById("treatment-select");
      const selectionInfo = document.getElementById("selection-info");
      const sendTreatmentSelectedBtn = document.getElementById(
        "send-treatment-selected-btn",
      );
      let treatmentsData = {
        Abcess: {
          key: "Abcess",
          output_treatment: "Abcess",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: "root",
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        Apicectomy: {
          key: "Apictomy",
          output_treatment: "Apictomy",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Bone Grafting": {
          key: "Bone Grafting",
          output_treatment: "BoneGraftingBlock",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Block Grafting": {
          key: "Block Grafting",
          output_treatment: "BoneGraftingBlock",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Gold Bridge Pontic": {
          key: "Gold Bridge Pontic",
          output_treatment: "Bridge_Gold",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: true,
        },
        "Porcelain Bonded Bridge": {
          key: "Porcelain Bonded Bridge",
          output_treatment: "PorcelainBondedBridge",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: true,
        },
        "Full Chrome Denture Fit": {
          key: "Full Chrome Denture Fit",
          output_treatment: "ChromePartialDenture",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Partial Chrome Denture": {
          key: "Partial Chrome Denture",
          output_treatment: "ChromePartialDenture",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        Clasp: {
          key: "Clasp",
          output_treatment: "Clasp",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: "crown",
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Closed Gap": {
          key: "Closed Gap",
          output_treatment: "ClosedGap",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Core BuildUp": {
          key: "Core BuildUp",
          output_treatment: "CoreBuildUp",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Tooth Prep For Crown": {
          key: "Tooth Prep For Crown",
          output_treatment: "ModifiedToothForCrown",
          crown: false,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Cerec Crown": {
          key: "Cerec Crown",
          output_treatment: "CrownForModedTooth_Cerec",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Composite Crown": {
          key: "Composite Crown",
          output_treatment: "CrownForModedTooth_Composite",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Emax Crown": {
          key: "Emax Crown",
          output_treatment: "CrownForModedTooth_Emax",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Empress Crown": {
          key: "Empress Crown",
          output_treatment: "CrownForModedTooth_Empress",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Gold Crown": {
          key: "Gold Crown",
          output_treatment: "CrownForModedTooth_Gold",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Metal Crown": {
          key: "Metal Crown",
          output_treatment: "CrownForModedTooth_Metal",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Porcelain Crown": {
          key: "Porcelain Crown",
          output_treatment: "CrownForModedTooth_Porcelain",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Silver Crown": {
          key: "Silver Crown",
          output_treatment: "CrownForModedTooth_Silver",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Zirconia Crown": {
          key: "Zirconia Crown",
          output_treatment: "CrownForModedTooth_Zirconia",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Arrested Caries": {
          key: "Arrested Caries",
          output_treatment: "Decay_ArrestedCaries",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        Cavity: {
          key: "Cavity",
          output_treatment: "Decay_Cavity",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        Caries: {
          key: "Caries",
          output_treatment: "Decay_Caries",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Early Caries": {
          key: "Early Caries",
          output_treatment: "Decay_EarlyCaries",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        Decay: {
          key: "Decay",
          output_treatment: "Decay",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Tooth Wear": {
          key: "Tooth Wear",
          output_treatment: "Decay_ToothWear",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Drifted Left": {
          key: "Drifted Left",
          output_treatment: "DriftedLeft",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Drifted Right": {
          key: "Drifted Right",
          output_treatment: "DriftedRight",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        Extraction: {
          key: "Extraction",
          output_treatment: "Extraction",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: true,
          remove_treatment_when_completed: true,
          bridge_treatment: false,
        },
        "Amalgam Filling": {
          key: "Amalgam Filling",
          output_treatment: "Filling_Amalgam",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Gold Filling": {
          key: "Gold Filling",
          output_treatment: "Filling_Gold",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Composite Filling": {
          key: "Composite Filling",
          output_treatment: "Filling_Composite",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Glass Ionomer Filling": {
          key: "Glass Ionomer Filling",
          output_treatment: "Filling_GlassIonomer",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Fissure Sealant": {
          key: "Fissure Sealant",
          output_treatment: "FissureSealant",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: "crown",
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Flexi Denture": {
          key: "Flexi Denture",
          output_treatment: "FlexiDenture",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Partial Acrylic Denture": {
          key: "Partial Acrylic Denture",
          output_treatment: "FlexiDenture",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Full Acrylic Denture": {
          key: "Full Acrylic Denture",
          output_treatment: "FlexiDenture",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Fractured Tooth - Large": {
          key: "Fractured Tooth - Large",
          output_treatment: "FracturedToothLarge",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Fractured Tooth - Small": {
          key: "Fractured Tooth - Small",
          output_treatment: "FracturedToothSmall",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Impacted Tooth": {
          key: "Impacted Tooth",
          output_treatment: "ImpactedToothCut",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Implant Bridge": {
          key: "Implant Bridge",
          output_treatment: "ImplantBridge",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: true,
        },
        "Implant Crown": {
          key: "Implant Crown",
          output_treatment: "CrownForImplant",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        Implant: {
          key: "Implant",
          output_treatment: "ImplantForCrown",
          crown: false,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        Inlay: {
          key: "Inlay",
          output_treatment: "Inlay",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Maryland Bridge": {
          key: "Maryland Bridge",
          output_treatment: "MarylandBridge",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: true,
        },
        "Maryland Wing": {
          key: "Maryland Wing",
          output_treatment: "MarylandWing",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        Onlay: {
          key: "Onlay",
          output_treatment: "Onlay",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: false,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Partially Erupted Tooth": {
          key: "Partially Erupted Tooth",
          output_treatment: "PartiallyEruptedTooth",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Pin Retention": {
          key: "Pin Retention",
          output_treatment: "PinRetention",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: "any",
          default_tooth_transparent: true,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Post & Core": {
          key: "Post & Core",
          output_treatment: "Post&Core",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: "any",
          default_tooth_transparent: true,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Retained Root": {
          key: "Retained Root",
          output_treatment: "RetainedRoot",
          crown: false,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Root Canal": {
          key: "Root Canal",
          output_treatment: "RootCanalTreatment",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: "any",
          default_tooth_transparent: true,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Sinus Drop": {
          key: "Sinus Drop",
          output_treatment: "Sinus_Drop",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Sinus Lift": {
          key: "Sinus Lift",
          output_treatment: "Sinus_Lift",
          crown: false,
          root: false,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Unerupted Tooth": {
          key: "Unerupted Tooth",
          output_treatment: "UneruptedTooth",
          crown: true,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Emax Veneer": {
          key: "Emax Veneer",
          output_treatment: "Veneers_Emax",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: "crown",
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Empress Veneer": {
          key: "Empress Veneer",
          output_treatment: "Veneers_Empress",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: "crown",
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Composite Veneer": {
          key: "Composite Veneer",
          output_treatment: "Veneers_Composite",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: "crown",
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Gold Veneer": {
          key: "Gold Veneer",
          output_treatment: "Veneers_Gold",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: "crown",
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Porcelain Veneer": {
          key: "Porcelain Veneer",
          output_treatment: "Veneers_Porcelain",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: "crown",
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Silver Veneer": {
          key: "Silver Veneer",
          output_treatment: "Veneers_Silver",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: "crown",
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Zirconia Veneer": {
          key: "Zirconia Veneer",
          output_treatment: "Veneers_Zirconia",
          crown: true,
          root: false,
          healthy_tooth_needed_when_missing: "crown",
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
        "Zygomatic Implant": {
          key: "Zygomatic Implant",
          output_treatment: "ZygomaticImplant",
          crown: false,
          root: true,
          healthy_tooth_needed_when_missing: null,
          default_tooth_transparent: false,
          full_tooth_treatment: true,
          remove_tooth_when_completed: false,
          remove_treatment_when_completed: false,
          bridge_treatment: false,
        },
      };
      // Populate dropdown
      Object.values(treatmentsData).forEach((treatment) => {
        const option = document.createElement("option");
        option.value = treatment.key;
        option.textContent = treatment.key;
        treatmentSelect.appendChild(option);
      });

      treatmentSelect.addEventListener("change", (event) => {
        if (!event.target.value) return;

        const treatmentKey = event.target.value;
        const message = {
          type: "treatment_selected",
          payload: { treatmentKey: treatmentKey },
        };

        if (iframe.contentWindow) {
          iframe.contentWindow.postMessage(message, "*");
          console.log("Sent message to iframe:", message);
        }
      });

      toggleEraserBtn.addEventListener("click", () => {
        isEraserActive = !isEraserActive;
        toggleEraserBtn.textContent = `Toggle Eraser (Active: ${isEraserActive})`;
        const message = {
          type: "eraser_state_change",
          payload: {
            active: isEraserActive,
          },
        };
        if (iframe.contentWindow) {
          iframe.contentWindow.postMessage(message, "*");
          console.log("Sent message to iframe:", message);
        }
      });

      clearTeethBtn.addEventListener("click", () => {
        const message = { type: "clear_teeth" };
        if (iframe.contentWindow) {
          iframe.contentWindow.postMessage(message, "*");
          console.log("Sent message to iframe:", message);
        }
      });

      function generateTeethData() {
        const teeth = {};
        const positions = [
          "UR8",
          "UR7",
          "UR6",
          "UR5",
          "UR4",
          "UR3",
          "UR2",
          "UR1",
          "UL1",
          "UL2",
          "UL3",
          "UL4",
          "UL5",
          "UL6",
          "UL7",
          "UL8",
          "LL8",
          "LL7",
          "LL6",
          "LL5",
          "LL4",
          "LL3",
          "LL2",
          "LL1",
          "LR1",
          "LR2",
          "LR3",
          "LR4",
          "LR5",
          "LR6",
          "LR7",
          "LR8",
        ];

        for (let i = 0; i < 32; i++) {
          const position_number = i + 1;
          teeth[position_number] = {
            position: positions[i],
            position_number: position_number,
            status: "status",
            lastTreatment: "01-01-2025",
            notes: "notes",
            marked_as_missing: false,
            marked_as_watched: false,
            treatments: [
              {
                Id: "Apictomy",
                name: "Apictomy",
                treatmentKey: "Apictomy",
                full_tooth_treatment: true,
                patient_treatment: false,
                remove_tooth_when_completed: false,
                remove_treatment_when_completed: false,
                bridge_treatment: false,
                missing_tooth_indicator: false,
                mixed_dentition: false,
                healthy_tooth_needed_when_missing: "Any",
              },
              {
                Id: "Clasp",
                name: "Clasp",
                treatmentKey: "Clasp",
                full_tooth_treatment: false,
                patient_treatment: false,
                remove_tooth_when_completed: false,
                remove_treatment_when_completed: false,
                bridge_treatment: false,
                missing_tooth_indicator: false,
                mixed_dentition: false,
                healthy_tooth_needed_when_missing: "Crown",
                need_default_tooth: false,
              },
            ],
          };
        }
        return teeth;
      }

      initializeTeethBtn.addEventListener("click", () => {
        const teethData = generateTeethData();
        const message = {
          type: "SET_TEETH_DATA",
          payload: teethData,
        };
        if (iframe.contentWindow) {
          iframe.contentWindow.postMessage(message, "*");
          console.log("Sent message to iframe:", message);
        }
      });

      sendMessageBtn.addEventListener("click", () => {
        const message = { type: "test-message", payload: "Hello from parent!" };
        if (iframe.contentWindow) {
          iframe.contentWindow.postMessage(message, "*");
          console.log("Sent message to iframe:", message);
        }
      });

      sendTreatmentSelectedBtn.addEventListener("click", () => {
        const message = {
          command: "treatment_selected",
          data: {
            Id: "T01",
            Name: "Veneer",
          },
        };
        if (iframe.contentWindow) {
          iframe.contentWindow.postMessage(JSON.stringify(message), "*");
          console.log("Sent message to iframe:", message);
        }
      });

      window.addEventListener("message", (event) => {
        let data = event.data;

        if (typeof data === "string") {
          try {
            data = JSON.parse(data);
          } catch (e) {
            return;
          }
        }

        if (data && data.source === "react-devtools-content-script") {
          return;
        }

        console.log("Received message from iframe:", data);

        if (data.type === "remove_tooth") {
          const { position } = data.payload;
          if (iframe.contentWindow) {
            iframe.contentWindow.postMessage(
              {
                type: "tooth_removed",
                payload: {
                  position: position,
                },
              },
              "*",
            );
          }
        }

        if (data.type === "mixed_dentition") {
          const { position, active } = data.payload;
          if (iframe.contentWindow) {
            iframe.contentWindow.postMessage(
              {
                type: "mixed_dentition_saved",
                payload: {
                  position: position,
                  active: active,
                },
              },
              "*",
            );
          }
        }

        if (data.type === "treatment_selected_with_tooth") {
          console.log("Treatment selected with tooth:", data.payload);
        }

        if (data.type === "position_and_surfaces_selected") {
          const newTreatment = { ...data.payload, id: Date.now() };
          treatments.push(newTreatment);
          renderTreatments();

          const responseMessage = {
            type: "charted_treatment_saved",
            ctId: "ct_" + Math.floor(Math.random() * 100000),
            position: data.payload.position,
          };

          if (iframe.contentWindow) {
            iframe.contentWindow.postMessage(responseMessage, "*");
            console.log(
              "Sent charted_treatment_saved to iframe:",
              responseMessage,
            );
          }
        }

        if (data.type === "charted_treatment_saved_response") {
          const { ctId, originalPayload } = data.payload;
          const treatmentIndex = treatments.findIndex(
            (t) => t.id === originalPayload.id,
          );
          if (treatmentIndex !== -1) {
            treatments[treatmentIndex].ctId = ctId;
            renderTreatments();
          }
        }

        if (data.type === "bridge_treatment_positions_selected") {
          console.log(
            "Received bridge_treatment_positions_selected",
            data.payload,
          );
          const responseMessage = {
            type: "bridge_treatment_saved",
            payload: {
              ctid: `ct_${Math.floor(Math.random() * 100000)}`,
              start_position: data.payload.start_position, // Echo back start
              end_position: data.payload.end_position, // Echo back end
            },
          };

          if (iframe.contentWindow) {
            iframe.contentWindow.postMessage(responseMessage, "*");
            console.log(
              "Sent bridge_treatment_saved to iframe:",
              responseMessage,
            );
          }
        }

        function renderTreatments() {
          const treatmentsList = document.getElementById("treatments-list");
          treatmentsList.innerHTML = "";
          treatments.forEach((treatment, index) => {
            const li = document.createElement("li");
            li.textContent = `Position: ${
              treatment.position
            }, Surfaces: ${JSON.stringify(treatment.surfaces)}`;
            if (treatment.ctId) {
              li.textContent += `, ctId: ${treatment.ctId}`;
            }

            const deleteBtn = document.createElement("button");
            deleteBtn.textContent = "X";
            deleteBtn.style.marginLeft = "10px";
            deleteBtn.onclick = () => {
              removeTreatment(index);
            };

            li.appendChild(deleteBtn);
            treatmentsList.appendChild(li);
          });
        }

        function removeTreatment(index) {
          const treatmentToRemove = treatments[index];
          treatments.splice(index, 1);
          renderTreatments();

          const message = {
            type: "remove_treatment",
            payload: {
              position: treatmentToRemove.position,
              ctId: treatmentToRemove.ctId,
            },
          };

          if (iframe.contentWindow) {
            iframe.contentWindow.postMessage(message, "*");
            console.log("Sent message to iframe:", message);
          }
        }
      });
    </script>
  </body>
</html>
