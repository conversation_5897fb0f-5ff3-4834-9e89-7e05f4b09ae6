import { useState, useRef, useEffect } from "react";
import Scene from "./components/scene";
import ViewIndicator from "./components/UI/view_indicator";
// import ViewSwitcher from "./components/UI/view_switcher";
import AnimationControls from "./components/UI/animation_controls";
// import TreatmentInput from "./components/UI/TreatmentInput"; // Added import
import { useTeeth } from "./context/TeethContext";
import { useTeethMessages } from "./hooks/useTeethMessages";
import { initializeScreenshotMessageListener } from "./utils/screenshotMessageHandler";
import { TREATMENT_MAP } from "./constants/dictionaries";
import "./App.css";

function App() {
  // View state
  const url = new URL(window.location.href);
  const viewParam = url.searchParams.get("view");
  const [currentView, setCurrentView] = useState(viewParam || "charting"); // Default to charting if no view param
  const [chartingThirdRow, setChartingThirdRow] = useState(false);
  const [isAnimationPlaying, setIsAnimationPlaying] = useState(false);
  const [rightClickData, setRightClickData] = useState(null);
  const [keyDown, setKeyDown] = useState(null);
  const [UIControlClicked, setUIConrolClicked] = useState("");
  const [singleTreatmentData, setSingleTreatmentData] = useState(null);

  // Get teeth state from context
  const {
    patientTeeth,
    setPatientTeeth,
    hoveredTooth,
    setHoveredTooth,
    selectedTooth,
    setSelectedTooth,
    hoveredMDTooth,
    setHoveredMDTooth,
    selectedMDTooth,
    setSelectedMDTooth,
    bridgeStart,
    setBridgeStart,
    selectedSurfaces,
    setSelectedSurfaces,
    selectedTreatment,
    setSelectedTreatment,
    eraserToolActive,
    missingToothActive,
    setMissingToothActive,
    resetTooth,
    setResetTooth,
    watchTooth,
    setWatchTooth,
    mixedDentation,
    setMixedDentation,
    // setAllTeethTreatment - Uncomment if needed
  } = useTeeth();

  const guardedPatientTeeth = patientTeeth || {};

  const handleSingleTreatmentSelect = (data) => {
    setSingleTreatmentData(data);
    setCurrentView("single_treatment");
  };

  // Initialize teeth message handlers
  useTeethMessages({
    handleTreatmentSelected: (payload) => {
      handleTreatmentSelect(payload.treatmentKey);
    },
    handleSingleTreatmentSelect,
  }); // Used for message handlers

  // Initialize screenshot message listener
  useEffect(() => {
    // Get the full teeth context
    const teethContext = {
      patientId: patientTeeth.patientId,
      patientTeeth,
    };

    // Initialize the screenshot message listener
    const cleanup = initializeScreenshotMessageListener(teethContext);

    return cleanup;
  }, [patientTeeth]);

  // No need to initialize teeth data here, it's handled by the AppWrapper

  // Pointer refs for skull and jaw
  const skullPointersRef = useRef(new Map());
  const jawPointersRef = useRef(new Map());
  const chartingFVPointersRef = useRef(new Map());
  const chartingTVPointersRef = useRef(new Map());
  const chartingBVPointersRef = useRef(new Map());
  const chartingWatchToolPointersRef = useRef(new Map());
  const chartingFVChildPointersRef = useRef(new Map());
  const chartingMDPointersRef = useRef(new Map());
  const chartingMDChildPointersRef = useRef(new Map());
  const previewTreatmentFVPointerRef = useRef();
  const previewTreatmentTVPointerRef = useRef();
  const controlsRef = useRef();

  // Toggle between skull and jaw views
  // const toggleView = () => {
  //   setCurrentView(currentView === "skull" ? "jaw" : "skull");
  // };

  const resetView = () => {
    if (typeof controlsRef.current?.resetCamera === "function") {
      controlsRef.current?.resetCamera();
    }
  };

  const openRightClickModal = (data) => {
    setRightClickData(data);
  };

  const closeRightClickModal = () => {
    setRightClickData(null);
  };

  const applyMissingTooth = (tooth) => {
    if (tooth) {
      window.parent.postMessage(
        {
          type: "remove_tooth",
          payload: {
            position: patientTeeth[tooth].position,
          },
        },
        "*",
      );
    }
  };

  const applyWatchTooth = (tooth) => {
    if (tooth) {
      setWatchTooth(tooth);
    }
  };

  const applyMixedDentation = (tooth) => {
    if (tooth && guardedPatientTeeth[tooth]) {
      const toothData = guardedPatientTeeth[tooth];
      const newActiveState = !toothData.mixed_dentition;
      window.parent.postMessage(
        {
          type: "mixed_dentition",
          payload: {
            active: newActiveState,
            position: toothData.position,
          },
        },
        "*",
      );
    }
  };

  const applyResetTooth = (tooth) => {
    if (tooth) {
      setResetTooth(tooth);

      setPatientTeeth((prevTeeth) => ({
        ...prevTeeth,
        [tooth]: {
          ...prevTeeth[tooth],
          status: "healthy",
          marked_as_missing: false,
          marked_as_watched: false,
          lastTreatment: null,
          notes: null,
          treatments: [],
        },
      }));
    }
  };

  // Handler for initializing test teeth data
  // const handleInitializeTeethTest = () => {
  //   // Sample teeth data
  //   const sampleTeethData = {
  //     1: {
  //       position: "UL8",
  //       status: "filled",
  //       lastTreatment: "2024-02-20",
  //       notes: "Large filling placed",
  //       marked_as_missing: false,
  //       marked_as_watched: false,
  //       treatments: [
  //         {
  //           id: "111",
  //           ctid: "222",
  //           name: "Filling",
  //           full_tooth_treatment: false,
  //           need_default_tooth: false,
  //           default_tooth_transparent: false,
  //           created_at: "2024-02-20",
  //           surfaces: {
  //             DistalOcclusal: { decaySeverity: 1, fillingSize: 1 },
  //             Distal: { decaySeverity: 1, fillingSize: 1 },
  //             Mesial: { decaySeverity: 1, fillingSize: 1 },
  //           },
  //         },
  //       ],
  //     },
  //     2: {
  //       position: "UL7",
  //       status: "filled",
  //       lastTreatment: "2024-02-20",
  //       notes: "Large filling placed",
  //       marked_as_missing: false,
  //       marked_as_watched: false,
  //       treatments: [
  //         {
  //           id: "112",
  //           ctid: "223",
  //           name: "Filling",
  //           full_tooth_treatment: false,
  //           need_default_tooth: false,
  //           default_tooth_transparent: false,
  //           created_at: "2024-02-20",
  //           surfaces: {
  //             DistalOcclusal: { decaySeverity: 1, fillingSize: 1 },
  //             Distal: { decaySeverity: 1, fillingSize: 1 },
  //             Mesial: { decaySeverity: 1, fillingSize: 1 },
  //           },
  //         },
  //       ],
  //     },
  //     3: {
  //       position: "UL6",
  //       status: "filled",
  //       lastTreatment: "2024-02-20",
  //       notes: "Large filling placed",
  //       marked_as_missing: false,
  //       marked_as_watched: false,
  //       treatments: [
  //         {
  //           id: "113",
  //           ctid: "224",
  //           name: "Filling",
  //           full_tooth_treatment: false,
  //           need_default_tooth: false,
  //           default_tooth_transparent: false,
  //           created_at: "2024-02-20",
  //           surfaces: {
  //             DistalOcclusal: { decaySeverity: 1, fillingSize: 1 },
  //             Distal: { decaySeverity: 1, fillingSize: 1 },
  //             Mesial: { decaySeverity: 1, fillingSize: 1 },
  //           },
  //         },
  //       ],
  //     },
  //     4: {
  //       position: "UL5",
  //       status: "infected",
  //       lastTreatment: "2024-02-20",
  //       notes: "Infection detected",
  //       marked_as_missing: false,
  //       marked_as_watched: false,
  //       treatments: [
  //         {
  //           id: "114",
  //           ctid: "225",
  //           name: "BoneGraft",
  //           full_tooth_treatment: true,
  //           need_default_tooth: false,
  //           default_tooth_transparent: false,
  //           created_at: "2024-02-20",
  //         },
  //       ],
  //     },
  //   };

  //   // Simulate receiving a message from UPOD
  //   window.dispatchEvent(
  //     new MessageEvent("message", {
  //       data: {
  //         type: "initialize_teeth",
  //         teeth: Object.entries(sampleTeethData).map(([number, tooth]) => ({
  //           ...tooth,
  //           position_number: number,
  //         })),
  //       },
  //     }),
  //   );
  // };

  // // Handler for clearing teeth data
  // const handleClearTeethTest = () => {
  //   // Simulate receiving a message from UPOD
  //   window.dispatchEvent(
  //     new MessageEvent("message", {
  //       data: {
  //         type: "clear_teeth",
  //       },
  //     }),
  //   );
  // };

  // Updated animation control handlers to handle both jaw and teeth
  const handlePlayAnimation = () => {
    setIsAnimationPlaying(true);

    // Play both jaw and teeth animations
    if (window.jawAnimationControls) {
      window.jawAnimationControls.play();
    }

    if (window.teethAnimationControls) {
      window.teethAnimationControls.play();
    }
    // The teeth animations will be controlled through the jaw animation controls
    // that we've linked in the Teeth component
  };

  const handleResetAnimation = () => {
    // Reset both jaw and teeth animations
    setIsAnimationPlaying(false);

    if (window.jawAnimationControls) {
      window.jawAnimationControls.reset();
      setIsAnimationPlaying(false);
    }

    if (window.teethAnimationControls) {
      window.teethAnimationControls.reset();
    }
    // The teeth animations will be reset automatically through the linked controls
  };

  // Clear pointers when switching views to prevent conflicts
  useEffect(() => {
    if (currentView === "skull") {
      jawPointersRef.current.clear();
    } else if (currentView === "jaw") {
      skullPointersRef.current.clear();
    }
  }, [currentView]);

  // Update animation playing state based on jaw controls

  useEffect(() => {
    const checkAnimationPlaying = () => {
      if (window.jawAnimationControls) {
        setIsAnimationPlaying(window.jawAnimationControls.isPlaying());
      }
    };

    // Check initially and set up interval to check periodically
    checkAnimationPlaying();
    const interval = setInterval(checkAnimationPlaying, 200);

    return () => clearInterval(interval);
  }, []);

  // Cleanup animation controls on unmount
  useEffect(() => {
    return () => {
      if (window.jawAnimationControls) {
        window.jawAnimationControls.reset();
      }
      if (window.teethAnimationControls) {
        window.teethAnimationControls.reset();
      }
    };
  }, []);

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data.type === "tooth_removed") {
        const { position } = event.data.payload;
        const toothNumber = Object.keys(patientTeeth).find(
          (key) => patientTeeth[key].position === position,
        );
        if (toothNumber) {
          setMissingToothActive(toothNumber);
        }
      } else if (event.data.type === "mixed_dentition_saved") {
        const { position } = event.data.payload;
        const toothNumber = Object.keys(patientTeeth).find(
          (key) => patientTeeth[key].position === position,
        );
        if (toothNumber) {
          setMixedDentation(toothNumber);
        }
      } else if (event.data.type === "bridge_treatment_saved") {
        const { ctid, start_position, end_position } = event.data.payload;

        // Find tooth numbers from positions
        const startToothNumber = Object.keys(patientTeeth).find(
          (key) => patientTeeth[key].position === start_position,
        );
        const endToothNumber = Object.keys(patientTeeth).find(
          (key) => patientTeeth[key].position === end_position,
        );

        if (startToothNumber && endToothNumber && selectedTreatment) {
          const newTreatment = {
            ...selectedTreatment,
            ctid: ctid,
            bridge_start_position: start_position,
            bridge_end_position: end_position,
          };

          const minTooth = Math.min(
            parseInt(startToothNumber),
            parseInt(endToothNumber),
          );
          const maxTooth = Math.max(
            parseInt(startToothNumber),
            parseInt(endToothNumber),
          );

          setPatientTeeth((prevTeeth) => {
            const newTeeth = { ...prevTeeth };
            for (let i = minTooth; i <= maxTooth; i++) {
              if (newTeeth[i]) {
                newTeeth[i] = {
                  ...newTeeth[i],
                  treatments: [...(newTeeth[i].treatments || []), newTreatment],
                };
              }
            }
            return newTeeth;
          });

          setBridgeStart(null);
          setSelectedTooth(null);
        }
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, [patientTeeth, setMissingToothActive, setPatientTeeth]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "r" && !keyDown) {
        setKeyDown("r");
        //
      } else if (
        (event.key === "Control" || event.key === "Meta") &&
        !keyDown
      ) {
        setKeyDown("cntrl");
        //
      }
    };

    const handleKeyUp = (event) => {
      if (event.key === "r") {
        setKeyDown(null);
        setBridgeStart(null);
        //
      } else if (event.key === "Control" || event.key === "Meta") {
        setKeyDown(null);
        //
      }
    };

    // Add event listeners for keydown and keyup
    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    // Cleanup event listeners on unmount
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, [keyDown]);

  const handleDefaultTooth = (healthy_tooth_need) => {
    let addDefaultTooth = true;
    if (!healthy_tooth_need) {
      addDefaultTooth = false;
    } else if (healthy_tooth_need === "any") {
      addDefaultTooth = true;
    } else if (healthy_tooth_need === "root") {
      Object.values(patientTeeth).forEach((tooth) => {
        if (tooth !== "patientType" && tooth !== "patientId") {
          tooth.treatments?.forEach((treatment) => {
            if (TREATMENT_MAP[treatment.treatmentKey].root) {
              addDefaultTooth = false;
            }
          });
        }
      });
    } else if (healthy_tooth_need === "crown") {
      Object.values(patientTeeth).forEach((tooth) => {
        if (tooth !== "patientType" && tooth !== "patientId") {
          tooth.treatments?.forEach((treatment) => {
            if (TREATMENT_MAP[treatment.treatmentKey].crown) {
              addDefaultTooth = false;
            }
          });
        }
      });
    }

    return addDefaultTooth;
  };

  const handleTreatmentSelect = (key) => {
    const treatmentSelectedObj = TREATMENT_MAP[key];
    if (!treatmentSelectedObj) return;

    const treatmentObj = {
      Id: "111",
      ctid: "222",
      completed: false,
      name: treatmentSelectedObj.output_treatment,
      treatmentKey: key,
      full_tooth_treatment: treatmentSelectedObj.full_tooth_treatment,
      patient_treatment: false,
      remove_tooth_when_completed:
        treatmentSelectedObj.remove_tooth_when_completed,
      remove_treatment_when_completed:
        treatmentSelectedObj.remove_treatment_when_completed,
      bridge_treatment: treatmentSelectedObj.bridge_treatment,
      missing_tooth_indicator: false,
      need_default_tooth:
        treatmentSelectedObj.need_default_tooth ||
        handleDefaultTooth(
          treatmentSelectedObj.healthy_tooth_needed_when_missing,
        ),
      default_tooth_transparent: treatmentSelectedObj.default_tooth_transparent,
      mixed_dentition: false,
      created_at: "01-01-2025",
      completed_at: null,
    };
    setSelectedTreatment(treatmentObj);
  };

  const applytoAllTeeth = () => {
    if (Object.keys(selectedTreatment).length !== 0) {
      setPatientTeeth((prevTeeth) => {
        const newTeeth = Object.keys(prevTeeth).reduce((acc, tooth) => {
          if (tooth !== "patientType" && tooth !== "patientId") {
            acc[tooth] = {
              ...prevTeeth[tooth],
              treatments: [...prevTeeth[tooth].treatments, selectedTreatment],
            };
          }
          return acc;
        }, {});
        return newTeeth;
      });
      setUIConrolClicked(`apply to all teeth ${selectedTreatment.name}`);
    }
  };

  const resetTeeth = () => {
    setPatientTeeth((prevTeeth) => {
      const resetTeeth = Object.keys(prevTeeth).reduce((acc, tooth) => {
        acc[tooth] = {
          ...prevTeeth[tooth],
          status: "healthy",
          marked_as_missing: false,
          marked_as_watched: false,
          lastTreatment: null,
          notes: null,
          treatments: [],
        };
        return acc;
      }, {});
      return resetTeeth;
    });
    setChartingThirdRow(false);
    setUIConrolClicked("reset teeth");
  };

  return (
    <div className="app">
      {(currentView === "skull" || currentView === "jaw") && (
        <div className="app-controls">
          <div className="button-group">
            {/* <h4>Data Management</h4>
          <button onClick={handleInitializeTeethTest}>Initialize Test Teeth</button>
          <button onClick={handleClearTeethTest}>Clear Teeth</button> */}
            {/* <button onClick={() => setAllTeethTreatment("BoneGraft")}>Apply BoneGraft to All</button> */}
          </div>
          {/* <div className="button-group">
          <h4>Treatment Actions</h4>
          <button onClick={() => teethMessages.selectTreatment({ id: 123, name: "Filling", full_tooth_treatment: false })}>Select Filling</button>
          <button onClick={() => teethMessages.selectPositionAndSurfaces("UL8", ["Mesial", "Distal"])}>Select UL8 Surfaces</button>
          <button onClick={() => selectedTooth && teethMessages.removeTooth(patientTeeth[selectedTooth]?.position)}>Remove Selected Tooth</button>
        </div>

        <div className="button-group">
          <h4>Other Actions</h4>
          <button onClick={() => teethMessages.toothRendered()}>Send Tooth Rendered</button>
          <button onClick={() => selectedTooth && teethMessages.viewToothHistory(patientTeeth[selectedTooth]?.position)}>View Tooth History</button>
          <button onClick={() => teethMessages.setMixedDentition(true, "UL5")}>Set UL5 Mixed Dentition</button>
          <button onClick={() => teethMessages.treatmentRemoved("UL8", "222")}>Remove UL8 Treatment</button>
        </div> */}
        </div>
      )}
      {(currentView === "skull" ||
        currentView === "jaw" ||
        currentView === "single_treatment") && (
        <>
          <ViewIndicator currentView={currentView} />
          {/* <ViewSwitcher currentView={currentView} toggleView={toggleView} /> */}
          {currentView === "jaw" && (
            <AnimationControls
              onPlay={handlePlayAnimation}
              onReset={handleResetAnimation}
              isPlaying={isAnimationPlaying}
            />
          )}
        </>
      )}
      {currentView === "charting" && (
        <div className="ui-controls-left" style={{ left: "250px" }}>
          <button className="view-switcher" onClick={applytoAllTeeth}>
            Apply to All Teeth
          </button>
        </div>
      )}
      {currentView === "charting" && (
        <div className="ui-controls-left" style={{ left: "420px" }}>
          <button className="view-switcher" onClick={resetTeeth}>
            Reset Teeth
          </button>
        </div>
      )}
      {(currentView === "charting" ||
        currentView === "skull" ||
        currentView === "jaw") && (
        <div
          className={
            currentView === "charting" ? "ui-controls-left" : "ui-controls"
          }
          style={currentView === "charting" ? { left: "590px" } : {}}
        >
          <select
            className="view-select-dropdown"
            value={currentView}
            onChange={(e) => setCurrentView(e.target.value)}
          >
            <option value="charting">Charting</option>
            <option value="skull">Skull</option>
            <option value="jaw">Jaw</option>
          </select>
        </div>
      )}
      {currentView === "charting" && (
        <div className="ui-controls">
          <button className="view-switcher" onClick={resetView}>
            Reset View
          </button>
        </div>
      )}
      <Scene
        currentView={currentView}
        controlsRef={controlsRef}
        chartingThirdRow={chartingThirdRow}
        patientTeeth={guardedPatientTeeth}
        setPatientTeeth={
          currentView === "single_treatment" ? () => {} : setPatientTeeth
        }
        hoveredTooth={currentView === "single_treatment" ? null : hoveredTooth}
        setHoveredTooth={
          currentView === "single_treatment" ? () => {} : setHoveredTooth
        }
        hoveredMDTooth={
          currentView === "single_treatment" ? null : hoveredMDTooth
        }
        setHoveredMDTooth={
          currentView === "single_treatment" ? () => {} : setHoveredMDTooth
        }
        selectedTooth={
          currentView === "single_treatment" ? null : selectedTooth
        }
        setSelectedTooth={
          currentView === "single_treatment" ? () => {} : setSelectedTooth
        }
        selectedMDTooth={
          currentView === "single_treatment" ? null : selectedMDTooth
        }
        setSelectedMDTooth={
          currentView === "single_treatment" ? () => {} : setSelectedMDTooth
        }
        bridgeStart={currentView === "single_treatment" ? null : bridgeStart}
        setBridgeStart={
          currentView === "single_treatment" ? () => {} : setBridgeStart
        }
        selectedSurfaces={
          currentView === "single_treatment" ? null : selectedSurfaces
        }
        setSelectedSurfaces={
          currentView === "single_treatment" ? () => {} : setSelectedSurfaces
        }
        selectedTreatment={
          currentView === "single_treatment" ? null : selectedTreatment
        }
        eraserToolActive={eraserToolActive}
        skullPointersRef={
          currentView === "single_treatment" ? new Map() : skullPointersRef
        }
        jawPointersRef={
          currentView === "single_treatment" ? new Map() : jawPointersRef
        }
        chartingFVPointersRef={
          currentView === "single_treatment" ? new Map() : chartingFVPointersRef
        }
        chartingTVPointersRef={
          currentView === "single_treatment" ? new Map() : chartingTVPointersRef
        }
        chartingBVPointersRef={
          currentView === "single_treatment" ? new Map() : chartingBVPointersRef
        }
        chartingWatchToolPointersRef={
          currentView === "single_treatment"
            ? new Map()
            : chartingWatchToolPointersRef
        }
        chartingFVChildPointersRef={
          currentView === "single_treatment"
            ? new Map()
            : chartingFVChildPointersRef
        }
        chartingMDPointersRef={
          currentView === "single_treatment" ? new Map() : chartingMDPointersRef
        }
        chartingMDChildPointersRef={
          currentView === "single_treatment"
            ? new Map()
            : chartingMDChildPointersRef
        }
        previewTreatmentFVPointerRef={previewTreatmentFVPointerRef}
        previewTreatmentTVPointerRef={previewTreatmentTVPointerRef}
        isAnimationPlaying={isAnimationPlaying}
        singleTreatmentData={singleTreatmentData}
        rightClickData={rightClickData}
        openRightClickModal={openRightClickModal}
        closeRightClickModal={closeRightClickModal}
        applyMissingTooth={applyMissingTooth}
        missingToothActive={missingToothActive}
        setMissingToothActive={setMissingToothActive}
        applyResetTooth={applyResetTooth}
        resetTooth={resetTooth}
        setResetTooth={setResetTooth}
        applyWatchTooth={applyWatchTooth}
        watchTooth={watchTooth}
        setWatchTooth={setWatchTooth}
        applyMixedDentation={applyMixedDentation}
        mixedDentation={mixedDentation}
        setMixedDentation={setMixedDentation}
        keyDown={keyDown}
        setChartingThirdRow={setChartingThirdRow}
        UIControlClicked={UIControlClicked}
      />
    </div>
  );
}

export default App;
