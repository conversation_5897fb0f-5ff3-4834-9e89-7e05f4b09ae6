import { Vector3 } from "three";
import { useRef, useEffect } from "react";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import * as THREE from "three";
import {
  createBlackMaterial,
  createTransparentMaterial,
  createRoughWhiteMaterial,
  createGoldMaterial,
  createMetalMaterial,
  createEmaxMaterial,
  createPorcelainMaterial,
  createZirconiaMaterial,
  createCompositeMaterial,
  createEmpressMaterial,
  createCerecMaterial,
  BoneGraftingBlockMaterial,
  createSilverMaterial,
} from "../constants/materials";
import { teethNumbersWithNoTreatment } from "../constants/dictionaries";
import { TREATMENT_MAP } from "../constants/dictionaries";
import { sendChartedTreatmentRemoved } from "../utils/teethMessageHandler";

export const createDefaultMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffffff,
    metalness: 0.3,
    roughness: 0.2,
    transparent: false,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export function ChartingTeeth({
  patientTeeth = {},
  setPatientTeeth,
  pointersRef,
  teethView,
  teethRef,
  selectedTooth,
  setSelectedTooth,
  bridgeStart,
  setBridgeStart,
  eraserToolActive,
  selectedTreatment,
  chartingThirdRow,
  missingToothActive,
  setMissingToothActive,
  resetTooth,
  setResetTooth,
  keyDown,
  chartingMDChildPointersRef,
  setChartingThirdRow,
  UIControlClicked,
  previewTreatmentFVPointerRef,
  previewTreatmentTVPointerRef,
}) {
  const internalTeethRef = useRef(new Map());
  const loaderRef = useRef(new GLTFLoader());

  useEffect(() => {
    if (teethRef) {
      teethRef.current = internalTeethRef.current;
    }
  }, [teethRef]);

  const loadToothModel = async (
    number,
    treatment,
    pointer,
    isDefaultTransparent = false,
    marylandBridgeWing = null,
    preview = null,
    ctid = null,
  ) => {
    const treatmentsWithUniqueChartingFiles = [
      "BoneGraftingBlock",
      "ChromePartialDenture",
      "CrownForImplant",
      "CrownForModedTooth",
      "FlexiDenture",
      "ImpactedToothCut",
      "ImplantForCrown",
      "MarylandBridge",
      "MarylandWing",
      "PorcelainBondedBridge",
      "Sinus",
      "Veneers",
      "ZygomaticImplant",
    ];

    if (!chartingThirdRow && !preview) {
      if (treatment.includes("Maryland")) {
        setChartingThirdRow(true);
      }
    }

    let treatmentMaterial;
    let treatmentMorphMode;
    let treatmentName = treatment;
    if (treatment.includes("Veneers_")) {
      treatmentMaterial = treatment.split("_")[1];
      treatmentName = treatment.split("_")[0];
    }
    if (treatment.includes("CrownForImplant_")) {
      treatmentMaterial = treatment.split("_")[1];
      treatmentName = treatment.split("_")[0];
    }
    if (treatment.includes("PorcelainBondedBridge_")) {
      treatmentMaterial = treatment.split("_")[1];
      treatmentName = treatment.split("_")[0];
    }
    if (treatment.includes("CrownForModedTooth_")) {
      treatmentMaterial = treatment.split("_")[1];
      treatmentName = treatment.split("_")[0];
    }
    if (treatment.includes("Sinus")) {
      treatmentMorphMode = treatment.split("_")[1];
      treatmentName = treatment.split("_")[0];
    }
    if (treatment.includes("FVBridge_") || treatment.includes("TVBridge_")) {
      treatmentMaterial = treatment.split("_")[1];
      treatmentName = treatment.split("_")[0];
    }

    if (treatmentName === "MarylandWing") {
      treatmentName = "MarylandBridge";
    }

    // Add _C suffix
    let suffixPath = "";
    if (treatmentsWithUniqueChartingFiles.includes(treatmentName)) {
      suffixPath = "_C";
    }
    if (
      treatmentName === "Clasp" ||
      treatmentName === "PartiallyEruptedTooth" ||
      treatmentName === "UneruptedTooth"
    ) {
      // use State A for charting
      suffixPath = "_A";
    }

    let prefixPath = "";
    if (preview) {
      if (
        preview === "FV" &&
        (treatmentName.includes("ClosedGap") ||
          treatmentName.includes("Extraction") ||
          treatmentName.includes("BoneGraftingBlock") ||
          treatmentName.includes("MarylandBridge") ||
          treatmentName.includes("MarylandWing") ||
          treatmentName.includes("FlexiDenture") ||
          treatmentName.includes("ZygomaticImplant") ||
          treatmentName.includes("Sinus") ||
          treatmentName.includes("ChromePartialDenture"))
      ) {
        prefixPath = "FV";
      } else if (
        preview === "TV" &&
        (treatmentName.includes("ClosedGap") ||
          treatmentName.includes("Extraction") ||
          treatmentName.includes("FlexiDenture") ||
          treatmentName.includes("MarylandBridge") ||
          treatmentName.includes("MarylandWing") ||
          treatmentName.includes("ChromePartialDenture"))
      ) {
        prefixPath = "TV";
      }
    } else {
      if (
        teethView === "front" &&
        (treatmentName.includes("ClosedGap") ||
          treatmentName.includes("Extraction") ||
          treatmentName.includes("BoneGraftingBlock") ||
          treatmentName.includes("MarylandBridge") ||
          treatmentName.includes("MarylandWing") ||
          treatmentName.includes("FlexiDenture") ||
          treatmentName.includes("ZygomaticImplant") ||
          treatmentName.includes("Sinus") ||
          treatmentName.includes("ChromePartialDenture"))
      ) {
        prefixPath = "FV";
      } else if (
        teethView === "top" &&
        (treatmentName.includes("ClosedGap") ||
          treatmentName.includes("Extraction") ||
          treatmentName.includes("FlexiDenture") ||
          treatmentName.includes("MarylandBridge") ||
          treatmentName.includes("MarylandWing") ||
          treatmentName.includes("ChromePartialDenture"))
      ) {
        prefixPath = "TV";
      }
    }

    if (teethView === "front" && treatment.includes("FVBridge")) {
      prefixPath = (Number(number) + 1).toString() + "_";
    }
    if (teethView === "top" && treatment.includes("TVBridge")) {
      prefixPath = (Number(number) + 1).toString() + "_";
    }

    const modelPath =
      treatmentName === "Default"
        ? `https://upod.s3.eu-central-1.amazonaws.com/treatmentsV4/${treatmentName}/${number}.glb`
        : `https://upod.s3.eu-central-1.amazonaws.com/treatmentsV4/${
            treatmentName + suffixPath
          }/${number}_${prefixPath + treatmentName + suffixPath}.glb`;

    try {
      const gltf = await new Promise((resolve, reject) => {
        loaderRef.current.load(modelPath, resolve, undefined, reject);
      });

      if (gltf.scene.children.length === 0) {
        return;
      }

      let child = gltf.scene.children[0];
      if (treatmentName.includes("PorcelainBondedBridge")) {
        child.children[0].material = createPorcelainMaterial();
      }
      if (treatment.includes("MarylandWing")) {
        // remove all connectors
        child.children.forEach((mesh) => {
          if (mesh.name.includes("Connector")) {
            child.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
              if (Array.isArray(mesh.material)) {
                mesh.material.forEach((material) => material.dispose());
              } else {
                mesh.material.dispose();
              }
            }
          }
        });
      }
      if (treatment.includes("MarylandBridge")) {
        // remove all wings except bridge edges
        child.children
          .filter(
            (mesh) =>
              mesh.isMesh &&
              (mesh.name.includes("WingLeft") ||
                mesh.name.includes("WingRight") ||
                mesh.name.includes("Connector")),
          )
          .forEach((mesh) => {
            if (
              (mesh.name.includes("WingLeft") &&
                marylandBridgeWing !== "left") ||
              (mesh.name.includes("WingRight") &&
                marylandBridgeWing !== "right")
            ) {
              child.remove(mesh);
              if (mesh.geometry) mesh.geometry.dispose();
              if (mesh.material) {
                if (Array.isArray(mesh.material)) {
                  mesh.material.forEach((material) => material.dispose());
                } else {
                  mesh.material.dispose();
                }
              }
            }
            if (
              mesh.name.includes("Connector") &&
              (marylandBridgeWing == "left" || preview)
            ) {
              child.remove(mesh);
              if (mesh.geometry) mesh.geometry.dispose();
              if (mesh.material) {
                if (Array.isArray(mesh.material)) {
                  mesh.material.forEach((material) => material.dispose());
                } else {
                  mesh.material.dispose();
                }
              }
            }
          });
      }
      child.position.set(0, 0, 0);
      child.rotation.set(0, 0, 0);
      // Set a unique name for the tooth mesh
      if (treatment === "MarylandWing") {
        treatmentName = treatment;
      }
      if (treatmentMaterial) {
        child.name = `tooth_${number}_${treatmentName}_${treatmentMaterial}`;
      } else {
        child.name = `tooth_${number}_${treatmentName}`;
      }

      if (child.isMesh) {
        const originalMaterial = child.material.clone();
        originalMaterial.side = THREE.FrontSide;
        originalMaterial.depthWrite = true;
        let childMaterial = originalMaterial;
        child.renderOrder = 0;

        if (treatmentName === "Default" && isDefaultTransparent) {
          childMaterial = createTransparentMaterial();
          childMaterial.depthWrite = false;
          child.renderOrder = 1;
        }
        if (treatmentName === "ClosedGap") {
          childMaterial = createBlackMaterial();
        }
        if (treatmentName === "CoreBuildUp") {
          child.children[0].material.color = new THREE.Color(0xfdfd96);
        }
        if (treatmentName === "Apictomy") {
          childMaterial = createTransparentMaterial();
        }
        if (treatmentName === "RetainedRoot") {
          childMaterial = createRoughWhiteMaterial();
        }
        if (treatmentName.includes("Veneers")) {
          if (treatmentMaterial === "Emax") {
            childMaterial = createEmaxMaterial();
          } else if (treatmentMaterial === "Empress") {
            childMaterial = createEmpressMaterial();
          } else if (treatmentMaterial === "Composite") {
            childMaterial = createCompositeMaterial();
          } else if (treatmentMaterial === "Gold") {
            childMaterial = createGoldMaterial();
          } else if (treatmentMaterial === "Porcelain") {
            childMaterial = createPorcelainMaterial();
          } else if (treatmentMaterial === "Silver") {
            childMaterial = createSilverMaterial();
          } else if (treatmentMaterial === "Zirconia") {
            childMaterial = createZirconiaMaterial();
          } else {
            childMaterial = createRoughWhiteMaterial();
          }
        }
        if (treatmentName.includes("CrownForModedTooth")) {
          if (treatmentMaterial === "Gold") {
            childMaterial = createGoldMaterial();
          } else if (treatmentMaterial === "Metal") {
            childMaterial = createMetalMaterial();
          } else if (treatmentMaterial === "Porcelain") {
            childMaterial = createPorcelainMaterial();
          } else if (treatmentMaterial === "Cerec") {
            childMaterial = createCerecMaterial();
          } else if (treatmentMaterial === "Empress") {
            childMaterial = createEmpressMaterial();
          } else if (treatmentMaterial === "Composite") {
            childMaterial = createCompositeMaterial();
          } else if (treatmentMaterial === "Emax") {
            childMaterial = createEmaxMaterial();
          } else if (treatmentMaterial === "Silver") {
            childMaterial = createSilverMaterial();
          } else if (treatmentMaterial === "Zirconia") {
            childMaterial = createZirconiaMaterial();
          } else {
            childMaterial = createRoughWhiteMaterial();
          }
        }
        if (treatment.includes("CrownForImplant")) {
          if (treatmentMaterial === "Porcelain") {
            childMaterial = createPorcelainMaterial();
          }
        }
        if (treatment.includes("FVBridge") || treatment.includes("TVBridge")) {
          if (treatmentMaterial === "Gold") {
            childMaterial = createGoldMaterial();
          } else if (treatmentMaterial === "Metal") {
            childMaterial = createMetalMaterial();
          } else if (treatmentMaterial === "Porcelain") {
            childMaterial = createPorcelainMaterial();
          }
        }
        if (treatment.includes("FlexiDenture")) {
          childMaterial.color = new THREE.Color(0xfc8eac);
        }
        if (treatment.includes("BoneGraftingBlock")) {
          childMaterial = BoneGraftingBlockMaterial();
        }
        if (treatmentName.includes("Sinus")) {
          childMaterial.transparent = true;
          childMaterial.opacity = 0.3;
        }

        child.material = childMaterial;
        child.userData = {
          number: number,
          type: "tooth",
          isInteractive: true,
          originalMaterial: childMaterial,
          isTreatment: treatmentName !== "Default",
          ctid: ctid,
        };
        child.material.side = THREE.DoubleSide;

        if (treatmentName.includes("Sinus")) {
          for (let i = 0; i < 4; i++) {
            if (
              patientTeeth[Number(number) + i].treatments.some((treatment) =>
                treatment.name.includes("Sinus"),
              )
            ) {
              if (treatmentMorphMode === "Drop") {
                child.morphTargetInfluences[i] = 1;
              } else if (treatmentMorphMode === "Lift") {
                child.morphTargetInfluences[i] = 0;
              }
            }
          }
        }
      }

      if (
        number >= 9 &&
        number <= 24 &&
        !(
          treatmentName.includes("BoneGraftingBlock") ||
          treatmentName.includes("Maryland") ||
          treatment.includes("FVBridge") ||
          treatment.includes("TVBridge") ||
          treatment.includes("Sinus")
        )
      ) {
        const originalScale = new Vector3();
        child.getWorldScale(originalScale);
        child.scale.set(1, 1, 1);
        child.updateMatrixWorld(true);
        child.scale.set(
          -Math.abs(originalScale.x),
          -Math.abs(originalScale.y),
          -Math.abs(originalScale.z),
        );
      } else if (treatmentName.includes("PorcelainBondedBridge")) {
        const originalScale = new Vector3();
        child.getWorldScale(originalScale);
        child.scale.set(
          Math.abs(originalScale.x),
          Math.abs(originalScale.y),
          Math.abs(originalScale.z),
        );
      }
      if (treatmentName.includes("Sinus")) {
        child.rotation.set(-89.5, 0, 0);
      }

      pointer.add(child);

      internalTeethRef.current.set(parseInt(number, 10), child);
    } catch (error) {
      console.error(`Error loading model for tooth ${number}:`, error);
    }
  };

  const preLoadToothModelSetup = (
    number,
    treatment,
    pointer,
    preview = null,
    start = null,
    end = null,
  ) => {
    let treatmentName = treatment.name;
    const ctid = treatment.ctid;
    if (
      treatment.name.includes("Bridge_") &&
      !treatment.name.includes("Maryland") &&
      !treatment.name.includes("Implant")
    ) {
      treatmentName = "CrownForModedTooth" + "_" + treatment.name.split("_")[1];
    } else if (treatment.name.includes("ImplantBridge")) {
      treatmentName = "CrownForImplant";
    }
    if (
      !teethNumbersWithNoTreatment[treatmentName] ||
      !teethNumbersWithNoTreatment[treatmentName].includes(number)
    ) {
      if (treatmentName.includes("Sinus")) {
        if (Number(number) >= 1 && Number(number) <= 4) {
          loadToothModel(
            "1",
            treatmentName,
            pointersRef.current.get(parseInt("1", 10)),
            false,
            null,
            preview,
            ctid,
          );
        } else if (number >= 13 && number <= 16) {
          loadToothModel(
            "13",
            treatmentName,
            pointersRef.current.get(parseInt("13", 10)),
            false,
            null,
            preview,
            ctid,
          );
        }
      } else if (treatmentName.includes("MarylandBridge")) {
        if (
          !preview &&
          number !== "1" &&
          number !== "16" &&
          number !== "17" &&
          number !== "32" &&
          start &&
          end
        ) {
          if (Number(number) === start || number === "2" || number == "18") {
            loadToothModel(
              number,
              "MarylandBridge",
              pointer,
              false,
              "right",
              preview,
              ctid,
            );
          } else if (
            Number(number) === end ||
            number === "15" ||
            number == "31"
          ) {
            loadToothModel(
              number,
              "MarylandBridge",
              pointer,
              false,
              "left",
              preview,
              ctid,
            );
          } else {
            loadToothModel(
              number,
              "MarylandBridge",
              pointer,
              false,
              null,
              preview,
              ctid,
            );
          }
        } else {
          loadToothModel(
            number,
            "MarylandBridge",
            pointer,
            false,
            null,
            preview,
            ctid,
          );
        }
      } else {
        loadToothModel(
          number,
          treatmentName,
          pointer,
          false,
          null,
          preview,
          ctid,
        );
      }
      if (
        !preview &&
        number !== "16" &&
        number !== "32" &&
        treatment.bridge_treatment &&
        (treatmentName.includes("CrownForImplant") ||
          treatmentName.includes("PorcelainBondedBridge") ||
          treatmentName.includes("CrownForModedTooth")) &&
        start &&
        end &&
        Number(number) >= start &&
        Number(number) < end
      ) {
        let material = treatment.name.split("_")[1] ?? "";
        if (treatmentName.includes("PorcelainBondedBridge")) {
          material = "Porcelain";
        }
        loadToothModel(
          number,
          "FVBridge" + "_" + material,
          pointer,
          false,
          null,
          preview,
          ctid,
        );
        loadToothModel(
          number,
          "TVBridge" + "_" + material,
          pointer,
          false,
          null,
          preview,
          ctid,
        );
      }
      if (treatment.need_default_tooth) {
        // let isDefaultTransparent = false;
        // if (treatmentName === "Post&Core" || treatmentName === "RootCanalTreatment" || treatmentName === "PinRetention") {
        //     isDefaultTransparent = true;
        // }
        loadToothModel(
          number,
          "Default",
          pointer,
          treatment.default_tooth_transparent,
          null,
          preview,
        );
      }
    }
  };

  useEffect(() => {
    if (pointersRef?.current && Object.keys(patientTeeth).length > 0) {
      for (const toothNumber in patientTeeth) {
        const toothData = patientTeeth[toothNumber];
        const pointer = pointersRef.current.get(parseInt(toothNumber, 10));
        if (pointer) {
          if (toothData.treatments.length > 0) {
            toothData.treatments.forEach((treatment) => {
              preLoadToothModelSetup(toothNumber, treatment, pointer);
            });
          } else {
            loadToothModel(toothNumber, "Default", pointer);
          }
        }
      }
    }
  }, []);
  useEffect(() => {
    if (pointersRef?.current) {
      // Clear any existing children from pointers
      pointersRef.current.forEach((pointer) => {
        while (pointer.children.length > 0) {
          const child = pointer.children[0];
          pointer.remove(child);
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((material) => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        }
      });
    }
  }, [UIControlClicked]);

  const handleMissingTooth = (toothNumber) => {
    // Remove from both front and top view
    pointersRef.current.forEach((pointer, key) => {
      if (key === toothNumber) {
        // Iterate in reverse to avoid skipping elements
        for (let i = pointer.children.length - 1; i >= 0; i--) {
          const child = pointer.children[i];
          if (child.name.startsWith(`tooth_${toothNumber}_`)) {
            pointer.remove(child);

            // Dispose of geometry and materials
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach((material) => material.dispose());
              } else {
                child.material.dispose();
              }
            }
          }
        }
      }
    });

    // remove mixed dentition if it exists
    if (patientTeeth[toothNumber]?.mixed_dentition) {
      const pointer = chartingMDChildPointersRef.current.get(
        parseInt(toothNumber, 10),
      );
      for (let i = pointer.children.length - 1; i >= 0; i--) {
        const child = pointer.children[i];
        if (child.name === `${toothNumber}C`) {
          pointer.remove(child);

          // Dispose of geometry and materials
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((material) => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        }
      }
    }

    // Update state to mark the tooth as missing and remove all treatments
    setPatientTeeth((prevTeeth) => {
      const newTeeth = { ...prevTeeth };
      newTeeth[toothNumber].marked_as_missing = true;
      newTeeth[toothNumber].mixed_dentition = false;
      return newTeeth;
    });
  };

  const handleResetTooth = (toothNumber) => {
    pointersRef.current.forEach((pointer, key) => {
      if (key === toothNumber) {
        // Iterate in reverse to avoid skipping elements
        for (let i = pointer.children.length - 1; i >= 0; i--) {
          const child = pointer.children[i];
          if (child.name.startsWith(`tooth_${toothNumber}_`)) {
            pointer.remove(child);

            // Dispose of geometry and materials
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach((material) => material.dispose());
              } else {
                child.material.dispose();
              }
            }
          }
        }
      }
    });

    const pointer = pointersRef.current.get(parseInt(toothNumber, 10));
    loadToothModel(toothNumber, "Default", pointer);

    if (chartingThirdRow) {
      if (
        !Object.values(patientTeeth).some(
          (tooth) =>
            tooth !== toothNumber &&
            tooth.treatments?.some((treatment) =>
              treatment.name.includes("Maryland"),
            ),
        )
      ) {
        setChartingThirdRow(false);
      }
    }
  };

  const handleEraserTool = () => {
    if (!eraserToolActive || !selectedTooth) return; // Ensure eraser tool is active

    const toothNumber = selectedTooth;

    // Get the last treatment to remove it
    const lastTreatment = patientTeeth[toothNumber].treatments.at(-1);
    if (!lastTreatment) return;

    // Send message to parent that treatment was removed
    if (lastTreatment.ctid) {
      sendChartedTreatmentRemoved(lastTreatment.ctid);
    }

    // Remove treatment from both front and top views
    pointersRef.current.forEach((pointer, key) => {
      if (key === toothNumber) {
        // Collect children to remove
        const childrenToRemove = pointer.children.filter(
          (child) =>
            child.name === `tooth_${toothNumber}_${lastTreatment.name}`,
        );

        // Remove them safely
        childrenToRemove.forEach((child) => {
          pointer.remove(child);

          // Dispose geometry and materials properly
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((material) => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        });
      }
    });

    // Load default if no treatments left - this is checked if the treatments list has 1 last treatment remaining
    if (patientTeeth[toothNumber]?.treatments.length === 1) {
      const pointer = pointersRef.current.get(parseInt(toothNumber, 10));
      loadToothModel(toothNumber, "Default", pointer);
    }

    // apply opacity levels to the remaining treatments
    const remainingTreatments = patientTeeth[toothNumber]?.treatments.slice(
      0,
      -1,
    );
    if (remainingTreatments) {
      setTimeout(() => {
        applyMaterialOpacityLevels(remainingTreatments, selectedTooth);
      }, 10);
    }

    // Update state to remove the last treatment
    if (teethView === "top") {
      setPatientTeeth((prevTeeth) => {
        const newTeeth = { ...prevTeeth };

        if (newTeeth[toothNumber]?.treatments.length > 0) {
          // Remove last treatment from state
          newTeeth[toothNumber].treatments = newTeeth[
            toothNumber
          ].treatments.slice(0, -1);
        }

        if (chartingThirdRow) {
          if (
            !Object.values(newTeeth).some((tooth) =>
              tooth.treatments?.some((treatment) =>
                treatment.name.includes("Maryland"),
              ),
            )
          ) {
            setChartingThirdRow(false);
          }
        }
        return newTeeth;
      });
    }
  };

  const handleSelectedTreatment = () => {
    if (Object.keys(selectedTreatment).length === 0 || !selectedTooth) return;

    if (keyDown === "r" && !bridgeStart && selectedTreatment.bridge_treatment) {
      setBridgeStart(selectedTooth);
      return;
    }

    if (
      patientTeeth[selectedTooth]?.treatments.some(
        (treatment) => treatment.name === selectedTreatment.name,
      )
    )
      return;

    const toothNumber = selectedTooth;
    const pointer = pointersRef.current.get(parseInt(toothNumber, 10));

    const handleDefaultTooth = (healthy_tooth_need) => {
      let addDefaultTooth = true;
      if (!healthy_tooth_need) {
        addDefaultTooth = false;
      } else if (healthy_tooth_need === "any") {
        addDefaultTooth = true;
      } else if (healthy_tooth_need === "root") {
        patientTeeth[toothNumber]?.treatments?.forEach((treatment) => {
          if (TREATMENT_MAP[treatment.treatmentKey].root) {
            addDefaultTooth = false;
          }
        });
      } else if (healthy_tooth_need === "crown") {
        patientTeeth[toothNumber]?.treatments?.forEach((treatment) => {
          if (TREATMENT_MAP[treatment.treatmentKey].crown) {
            addDefaultTooth = false;
          }
        });
      }

      return addDefaultTooth;
    };

    if (TREATMENT_MAP[selectedTreatment.treatmentKey]) {
      selectedTreatment.need_default_tooth = handleDefaultTooth(
        TREATMENT_MAP[selectedTreatment.treatmentKey]
          .healthy_tooth_needed_when_missing,
      );
    }

    if (keyDown === "r" && bridgeStart && selectedTreatment.bridge_treatment) {
      let start = Number(bridgeStart);
      let end = Number(toothNumber);
      if (end < start) {
        [start, end] = [end, start]; // Swap if needed
      }

      // This loop should ONLY handle visual updates (loading models).
      for (let i = start; i <= end; i++) {
        const tooth = i.toString();
        const pointer = pointersRef.current.get(parseInt(tooth, 10));

        // Remove default teeth before adding treatment if no treatments belong to teeth
        if (patientTeeth[tooth]?.treatments.length === 0) {
          pointer.children.forEach((child) => {
            if (child.name.startsWith(`tooth_${tooth}_`)) {
              pointer.remove(child);
              if (child.geometry) child.geometry.dispose();
              if (child.material) {
                if (Array.isArray(child.material)) {
                  child.material.forEach((material) => material.dispose());
                } else {
                  child.material.dispose();
                }
              }
            }
          });
        }

        preLoadToothModelSetup(
          tooth,
          selectedTreatment,
          pointer,
          null,
          start,
          end,
        );

        const UpdatedListOfTreatments =
          patientTeeth[tooth]?.treatments.concat(selectedTreatment);
        if (UpdatedListOfTreatments) {
          setTimeout(() => {
            applyMaterialOpacityLevels(UpdatedListOfTreatments, tooth);
          }, 10);
        }
      }

      // After the loop, send ONE message for the entire bridge.
      const startPosition = patientTeeth[start]?.position;
      const endPosition = patientTeeth[end]?.position;

      if (startPosition && endPosition) {
        window.parent.postMessage(
          {
            type: "bridge_treatment_positions_selected",
            payload: {
              start_position: startPosition,
              end_position: endPosition,
            },
          },
          "*",
        );
      }

      setBridgeStart(null); // Keep this reset
    } else {
      // Remove default teeth before adding treatment if no treatments belong to teeth
      if (patientTeeth[toothNumber]?.treatments.length === 0) {
        pointer.children.forEach((child) => {
          if (child.name.startsWith(`tooth_${toothNumber}_`)) {
            pointer.remove(child);
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach((material) => material.dispose());
              } else {
                child.material.dispose();
              }
            }
          }
        });
      }

      // Load the new treatment model
      preLoadToothModelSetup(toothNumber, selectedTreatment, pointer);

      // Apply opacity levels to treatments
      const UpdatedListOfTreatments =
        patientTeeth[toothNumber]?.treatments.concat(selectedTreatment);
      if (UpdatedListOfTreatments) {
        setTimeout(() => {
          applyMaterialOpacityLevels(UpdatedListOfTreatments, toothNumber);
        }, 10);
      }

      // Update state to add the new treatment
      if (teethView === "top") {
        setPatientTeeth((prevTeeth) => {
          const newTeeth = { ...prevTeeth };
          newTeeth[toothNumber].treatments.push(selectedTreatment);
          const toothPosition = newTeeth[toothNumber]?.position;
          if (toothPosition) {
            window.parent.postMessage(
              {
                type: "position_and_surfaces_selected",
                payload: {
                  position: toothPosition,
                  surfaces: [],
                },
              },
              "*",
            );
          }
          return newTeeth;
        });
      }
    }
  };

  const applyMaterialOpacityLevels = (treatments, tooth) => {
    if (!tooth || treatments.length === 0) return;

    const toothNumber = tooth;
    const totalTreatments = treatments.length;

    if (totalTreatments === 1) return;

    // Define opacity range
    const minOpacity = 0.5;
    const maxOpacity = 1;

    // go over treatments available on selected tooth and set different opacity levels for each
    const opacityStep = (maxOpacity - minOpacity) / (totalTreatments - 1);

    treatments.forEach((treatment, index) => {
      const treatmentOpacity = maxOpacity - index * opacityStep;
      pointersRef.current.forEach((pointer) => {
        pointer.children.forEach((child) => {
          if (child.name === `tooth_${toothNumber}_${treatment.name}`) {
            if (child.material && child.material.opacity === 1) {
              if (Array.isArray(child.material)) {
                child.material.forEach((material) => {
                  material.transparent = true;
                  material.opacity = treatmentOpacity;
                });
              } else {
                child.material.transparent = true;
                child.material.opacity = treatmentOpacity;
              }
            }
          }
        });
      });
    });
  };

  // Attach event listeners to each tooth mesh
  useEffect(() => {
    if (selectedTooth) {
      if (
        teethView === "top" ||
        teethView === "front" ||
        (teethView === "back" && chartingThirdRow)
      ) {
        if (eraserToolActive) {
          handleEraserTool();
        } else if (
          selectedTreatment &&
          Object.keys(selectedTreatment).length !== 0
        ) {
          handleSelectedTreatment();
        }

        // Prevent double execution by delaying the reset
        setTimeout(() => {
          setSelectedTooth(null);
        }, 0);
      }
    }
  }, [selectedTooth, eraserToolActive]);

  useEffect(() => {
    if (missingToothActive) {
      handleMissingTooth(missingToothActive);
      setMissingToothActive(null);
    }
  }, [missingToothActive]);

  useEffect(() => {
    if (resetTooth) {
      if (
        teethView === "top" ||
        teethView === "front" ||
        (teethView === "back" && chartingThirdRow)
      ) {
        handleResetTooth(resetTooth);
        setResetTooth(null);
      }
    }
  }, [resetTooth]);

  useEffect(() => {
    if (!patientTeeth || !selectedTreatment) return;
    if (Object.keys(selectedTreatment).length !== 0) {
      const treatmentData = TREATMENT_MAP[selectedTreatment.treatmentKey];
      if (!treatmentData) return;
      const pointerFV = previewTreatmentFVPointerRef.current;
      const pointerTV = previewTreatmentTVPointerRef.current;

      // Clear any existing children from pointers
      while (pointerFV.children.length > 0) {
        const child = pointerFV.children[0];
        pointerFV.remove(child);
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((material) => material.dispose());
          } else {
            child.material.dispose();
          }
        }
      }
      while (pointerTV.children.length > 0) {
        const child = pointerTV.children[0];
        pointerTV.remove(child);
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((material) => material.dispose());
          } else {
            child.material.dispose();
          }
        }
      }

      selectedTreatment.need_default_tooth =
        treatmentData.healthy_tooth_needed_when_missing ? true : false;
      preLoadToothModelSetup("4", selectedTreatment, pointerFV, "FV");
      preLoadToothModelSetup("4", selectedTreatment, pointerTV, "TV");
    }
  }, [selectedTreatment]);

  return null;
}

ChartingTeeth.displayName = "ChartingTeeth";
export default ChartingTeeth;
