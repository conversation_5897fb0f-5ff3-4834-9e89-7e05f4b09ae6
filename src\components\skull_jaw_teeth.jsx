import { Vector3 } from "three";
import React, {
  useRef,
  useEffect,
  useState,
  forwardRef,
  useCallback,
} from "react";
import { useTeeth } from "../context/TeethContext";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { use<PERSON>rame, useThree } from "@react-three/fiber";
import { BASE_URL, TREATMENTS_VERSION } from "../config/api";
import * as THREE from "three";

// Import utility functions from modelUtils.js
import {
  modelCache,
  animationCache,
  sanitizeAnimationTracks,
  createPositionLockedMixer,
  getModelParts,
  cloneModel,
  findExactSurfaceMorphTarget,
  forceSceneUpdate,
  clearAllTeethFromScene,
  checkForStateSpecificModel,
} from "../utils/modelUtils";
import { determineHealthyPartVisibility } from "../utils/treatmentUtils";

// Import message utility functions
import { addTeethClearedListener } from "../utils/messageUtils";

// Import material utilities
import {
  applyAntiZFightingProperties,
  parseTreatmentName,
  applyMaterialByName,
} from "../utils/materialUtils";
import { ensureAllMaterialsTransparent } from "../utils/transparencyUtils";
import { BoneGraftingBlockMaterial } from "../constants/materials";

const SkullJawTeeth = forwardRef(
  ({ pointersRef, currentView, teethRefStateA: teethRef }, ref) => {
    // Get teeth data from context
    const { patientTeeth, getTreatmentVisibility } = useTeeth();
    // console.log("SkullJawTeeth: patientTeeth state on render:", patientTeeth);
    // console.log(patientTeeth);
    // Get the patient type from the teeth data
    const patientType = patientTeeth.patientType || "ADULT";
    const internalTeethRef = useRef(new Map());
    const loaderRef = useRef(new GLTFLoader());
    const mountedRef = useRef(true);
    const mixersRef = useRef(new Map());
    const actionsRef = useRef(new Map());
    const frameCountRef = useRef(0);
    const [isPlaying, setIsPlaying] = useState(false);
    const [isPaused, setIsPaused] = useState(false);
    const [loadingTeeth, setLoadingTeeth] = useState(new Map());
    const [isLoading, setIsLoading] = useState(false);
    const totalTeethToLoadRef = useRef(0);
    const isInitializedRef = useRef(false);
    const sinusModelRef = useRef({ R: null, L: null });

    // Store filling morph targets to preserve during animations
    const fillingMorphTargetsRef = useRef(new Map());

    // Store initial positions to prevent position changes during animations
    const initialPositionsRef = useRef(new Map());

    // We're using the imported findExactSurfaceMorphTarget function from modelUtils.js

    // Define cleanupTeeth function first
    // Get access to the scene and renderer from React Three Fiber
    const { scene, gl: renderer, camera } = useThree();

    // Function to reapply stored filling morph targets after animation resets
    const reapplyFillingMorphTargets = useCallback(() => {
      fillingMorphTargetsRef.current.forEach((morphTargets, meshKey) => {
        // Find the mesh by traversing the scene
        scene.traverse((obj) => {
          if (obj.isMesh && meshKey.includes(obj.uuid)) {
            morphTargets.forEach((severity, morphIndex) => {
              if (
                obj.morphTargetInfluences &&
                obj.morphTargetInfluences[morphIndex] !== undefined
              ) {
                obj.morphTargetInfluences[morphIndex] = severity;
              }
            });
          }
        });
      });
    }, [scene]);

    const cleanupTeeth = useCallback(() => {
      // Cleanup teeth

      // Always clean up mixers and actions
      mixersRef.current.forEach((mixer) => mixer.stopAllAction());
      mixersRef.current.clear();
      actionsRef.current.clear();

      // Clear initial positions
      initialPositionsRef.current.clear();

      // Always clear the refs
      internalTeethRef.current.clear();

      if (teethRef && teethRef.current) {
        teethRef.current.clear();
      }

      // Use the utility function to clear all teeth from the scene
      if (scene) {
        clearAllTeethFromScene(scene);
      }

      // Force a scene update using the utility function
      if (renderer && scene && camera) {
        forceSceneUpdate(renderer, scene, camera);
      }

      // Cleanup complete
    }, [
      internalTeethRef,
      teethRef,
      mixersRef,
      actionsRef,
      scene,
      renderer,
      camera,
    ]);

    // Setup resource path and event listeners
    useEffect(() => {
      loaderRef.current.setResourcePath(`${BASE_URL}/${TREATMENTS_VERSION}/`);
      loaderRef.current.setCrossOrigin("anonymous");

      // Add event listener for teeth-cleared event using the utility function
      const removeTeethClearedListener = addTeethClearedListener(() => {
        cleanupTeeth();
      });

      return () => {
        // Clean up the event listener
        removeTeethClearedListener();
      };
    }, [cleanupTeeth]);

    // Update teethRef when internalTeethRef changes
    useEffect(() => {
      if (teethRef && teethRef.current && internalTeethRef.current.size > 0) {
        internalTeethRef.current.forEach((tooth, number) => {
          teethRef.current.set(number, tooth);
        });
      }
    }, [internalTeethRef.current.size, currentView, teethRef]);

    const playAnimations = useCallback(() => {
      if (!isPaused) {
        frameCountRef.current = 0;
      }

      actionsRef.current.forEach((action) => {
        action.stop();
        action.reset();
      });

      // Reapply filling morph targets after animation reset
      reapplyFillingMorphTargets();

      actionsRef.current.forEach((action) => {
        action.play();
        action.setEffectiveWeight(1);
        action.paused = false;
      });

      setIsPlaying(true);
      setIsPaused(false);
    }, [
      isPaused,
      actionsRef,
      setIsPlaying,
      setIsPaused,
      reapplyFillingMorphTargets,
    ]);

    const pauseAnimations = () => {
      actionsRef.current.forEach((action) => {
        action.paused = true;
      });
      setIsPaused(true);
    };

    const resetAnimations = () => {
      frameCountRef.current = 0;

      actionsRef.current.forEach((action) => {
        action.stop();
        action.reset();
      });

      setIsPlaying(false);
      setIsPaused(false);
    };

    const processAnimations = useCallback(
      (
        toothNumber,
        animations,
        animatedObjectRoot,
        treatmentName,
        originalRootNameFromLoad,
        newRootNameFromLoad,
      ) => {
        if (!animatedObjectRoot || !animations || animations.length === 0) {
          return;
        }

        // Check if a mixer already exists for this tooth
        let mixer = mixersRef.current.get(toothNumber);
        if (!mixer) {
          // Create a position-locked mixer to prevent X/Z movement during animations
          mixer = createPositionLockedMixer(animatedObjectRoot);
          mixersRef.current.set(toothNumber, mixer);
        }

        animations.forEach((clip) => {
          // Debug: Check if clip has position tracks

          const retargetedTracks = [];
          clip.tracks.forEach((originalTrack) => {
            const parsedPath = THREE.PropertyBinding.parseTrackName(
              originalTrack.name,
            );
            let newTrackName = originalTrack.name; // Default to original
            let targetNodeFound = false;

            if (parsedPath.nodeName === originalRootNameFromLoad) {
              // Track targets the root node of the GLTF part, which is now animatedObjectRoot
              // Its name for binding is newRootNameFromLoad (which is animatedObjectRoot.name)
              newTrackName = `${newRootNameFromLoad}.${parsedPath.propertyName}`;
              if (parsedPath.propertyIndex) {
                newTrackName += `[${parsedPath.propertyIndex}]`;
              }
              targetNodeFound = true; // The root itself is the target
            } else {
              // Track targets a descendant node. Assume its name hasn't changed.
              // Search for it within animatedObjectRoot.
              const descendantNode = animatedObjectRoot.getObjectByName(
                parsedPath.nodeName,
              );
              if (descendantNode) {
                // Node found, original track name is valid relative to animatedObjectRoot
                newTrackName = originalTrack.name; // No change needed as getObjectByName will find it
                targetNodeFound = true;
              }
            }

            if (targetNodeFound) {
              const retargetedTrack = originalTrack.clone();
              retargetedTrack.name = newTrackName;
              retargetedTracks.push(retargetedTrack);

              // If this clip has position tracks, store initial positions for the *actual* node
              if (
                clip.userData?.hasPositionTracks &&
                originalTrack.name.endsWith(".position")
              ) {
                // Find the actual node in the animatedObjectRoot hierarchy using its current name
                const nodeNameToFind = newTrackName.split(".")[0];
                const nodeToStorePosition =
                  animatedObjectRoot.getObjectByName(nodeNameToFind);

                if (nodeToStorePosition) {
                  // Use UUID for robust node identification
                  const positionKey = nodeToStorePosition.uuid;

                  if (!initialPositionsRef.current.has(positionKey)) {
                    initialPositionsRef.current.set(positionKey, {
                      position: nodeToStorePosition.position.clone(),
                      isInitialLoad: true,
                      nodeName: nodeToStorePosition.name, // Store name for debugging
                    });
                  }
                }
              }
            }
          });

          if (retargetedTracks.length === 0 && clip.tracks.length > 0) {
            return; // Skip this clip if no tracks could be retargeted
          }

          // Ensure we only proceed if there are retargeted tracks
          if (retargetedTracks.length > 0) {
            const validClip = new THREE.AnimationClip(
              clip.name,
              clip.duration,
              retargetedTracks,
            );

            // Copy userData to preserve position track information
            validClip.userData = clip.userData;

            // Create a unique key for this action that includes the treatment name
            const actionKey = `${toothNumber}_${treatmentName}_${validClip.name}`;

            // Check if this action already exists
            if (!actionsRef.current.has(actionKey)) {
              const action = mixer.clipAction(validClip);
              action.setLoop(THREE.LoopOnce);
              action.clampWhenFinished = true;

              actionsRef.current.set(actionKey, action);
            } else {
              // No tracks to animate for this clip, so don't create an action
              return;
            }
          }
        }); // End of animations.forEach
      }, // This closing brace and comma for useCallback was at line 393
      [mixersRef, actionsRef, initialPositionsRef, currentView], // Added currentView as it's used in logging/debug, though not directly in logic
    );

    useFrame((_, delta) => {
      // Debug: Log animation state every 60 frames (approximately 1 second)

      if (isPlaying && !isPaused) {
        // Debug: Track what's happening during animation updates
        mixersRef.current.forEach((mixer) => {
          mixer.update(delta);

          // Debug: Check what changed after mixer update
          // scene.traverse((obj) => { // Commenting out detailed per-object before/after logging for brevity
          //   if (obj.name && beforeUpdate[obj.name]) {
          //     const before = beforeUpdate[obj.name];
          //     const posChanged = !obj.position.equals(before.position);
          //     const rotChanged = !obj.rotation.equals(before.rotation);
          //     const scaleChanged = !obj.scale.equals(before.scale);

          //     if (posChanged || rotChanged || scaleChanged) {
          //       transformationsApplied++;
          //       if (frameCountRef.current % 60 === 0) {
          //       }
          //     }
          //   }
          // });
        });

        // Smart position preservation (for sub-nodes, might be overridden by the above for root nodes if they have anims)
        initialPositionsRef.current.forEach((posData, nodeUuid) => {
          if (posData.isInitialLoad) {
            const objectToRestore = scene.getObjectByProperty("uuid", nodeUuid);
            if (objectToRestore) {
              // Check if this object is one of the locked root objects.
              // If so, its position is already being forced to (0,0,0) by the new logic,
              // so restoring its 'initial animated position' might be redundant or conflicting
              // if that initial animated position was not (0,0,0).
              // However, this initialPositionsRef is for *any* animated node, not just roots.
              // if (!lockedRootObjectsRef.current.has(nodeUuid)) { // Removed check as lockedRootObjectsRef is removed
              objectToRestore.position.copy(posData.position);
              // }
              posData.isInitialLoad = false; // Mark as no longer initial load
            }
          }
        });

        // Continuously reapply filling morph targets during animation
        reapplyFillingMorphTargets();

        const fps = 30;
        frameCountRef.current += delta * fps;

        // Debug: Log summary every second

        if (frameCountRef.current >= 30) {
          pauseAnimations();
        }
      }
    });

    useEffect(() => {
      const setupAnimationControls = () => {
        const teethControls = {
          play: playAnimations,
          pause: pauseAnimations,
          reset: resetAnimations,
          isPlaying: () => isPlaying,
          isPaused: () => isPaused,
          currentFrame: () => frameCountRef.current,
        };

        window.teethAnimationControls = teethControls;

        if (window.jawAnimationControls) {
          const originalJawPlay = window.jawAnimationControls.play;
          window.jawAnimationControls.play = () => {
            originalJawPlay();
            playAnimations();
          };

          window.jawAnimationControls._originalPlay = originalJawPlay;

          const originalJawReset = window.jawAnimationControls.reset;
          window.jawAnimationControls.reset = () => {
            originalJawReset();
            resetAnimations();
          };
          window.jawAnimationControls._originalReset = originalJawReset;
        }
      };

      setupAnimationControls();

      return () => {
        if (window.jawAnimationControls) {
          if (window.jawAnimationControls._originalPlay) {
            window.jawAnimationControls.play =
              window.jawAnimationControls._originalPlay;
          }
          if (window.jawAnimationControls._originalReset) {
            window.jawAnimationControls.reset =
              window.jawAnimationControls._originalReset;
          }
        }
      };
    }, [isPlaying, isPaused, playAnimations]);

    // Using the imported cloneModel function from modelUtils.js

    const markToothLoaded = useCallback(
      (number) => {
        if (!mountedRef.current) return;

        setLoadingTeeth((prev) => {
          const newMap = new Map(prev);
          newMap.set(number, true);
          return newMap;
        });
      },
      [mountedRef, setLoadingTeeth],
    );

    // Updated loadToothModel function with precise morph target handling and better error handling
    const loadToothModel = useCallback(
      async (number, toothData, pointer) => {
        // Skip teeth that are marked as missing or have a treatment with missing_tooth_indicator
        const hasMissingToothTreatment =
          toothData.treatments &&
          toothData.treatments.some(
            (treatment) => treatment.missing_tooth_indicator,
          );

        if (toothData.marked_as_missing || hasMissingToothTreatment) {
          // Mark the tooth as loaded even though we're not actually loading it
          markToothLoaded(number);
          return;
        }

        if (!mountedRef.current) return;

        const toothNumber = parseInt(number, 10);
        const existingTooth = internalTeethRef.current.get(toothNumber);
        if (existingTooth && existingTooth.parent === pointer) {
          markToothLoaded(number);
          return;
        }

        const existingParts = pointer.children.filter((c) =>
          c.name.startsWith(`tooth_${number}_`),
        );
        existingParts.forEach((part) => {
          pointer.remove(part);
          part.traverse((child) => {
            if (child.isMesh) {
              child.geometry?.dispose();
              if (Array.isArray(child.material)) {
                child.material.forEach((m) => m.dispose());
              } else {
                child.material?.dispose();
              }
            }
          });
        });

        // Sort treatments by creation date (newest first)
        const sortedTreatments = toothData.treatments
          ? [...toothData.treatments].sort((a, b) => {
              const dateA = new Date(a.created_at || 0);
              const dateB = new Date(b.created_at || 0);
              return dateB - dateA; // Newest first
            })
          : [];

        // Get model parts for all treatments
        const modelParts = getModelParts(toothData);
        let primaryModel = null;

        // Store decay surfaces for filling part (from the first/newest treatment)
        const decaySurfaces =
          sortedTreatments.length > 0 && sortedTreatments[0].surfaces
            ? Object.keys(sortedTreatments[0].surfaces)
            : [];

        for (const basePartName of modelParts) {
          let filesToLoadThisIteration = [];
          const toothIdInFilename =
            patientType === "CHILDREN" ? `${number}C` : number;

          if (basePartName === "Inlay") {
            filesToLoadThisIteration.push({
              originalTreatmentName: basePartName,
              modelFileIdentifier: `Inlay_${toothIdInFilename}_Base`,
              modelFileIdentifierForObjectName: "Inlay_Base",
            });
            filesToLoadThisIteration.push({
              originalTreatmentName: basePartName,
              modelFileIdentifier: `InlayTooth_${toothIdInFilename}_Base`,
              modelFileIdentifierForObjectName: "InlayTooth_Base",
            });
          } else if (basePartName === "Onlay") {
            filesToLoadThisIteration.push({
              originalTreatmentName: basePartName,
              modelFileIdentifier: `Onlay_${toothIdInFilename}_Base`,
              modelFileIdentifierForObjectName: "Onlay_Base",
            });
            filesToLoadThisIteration.push({
              originalTreatmentName: basePartName,
              modelFileIdentifier: `OnlayTooth_${toothIdInFilename}_Base`,
              modelFileIdentifierForObjectName: "OnlayTooth_Base",
            });
          } else {
            // Standard behavior: modelFileIdentifier is the basePartName itself, or with toothId for Default
            const modelFileId =
              basePartName === "Default"
                ? toothIdInFilename
                : `${toothIdInFilename}_${basePartName}`;
            filesToLoadThisIteration.push({
              originalTreatmentName: basePartName,
              modelFileIdentifier: modelFileId,
              modelFileIdentifierForObjectName: basePartName,
            });
          }

          for (const loadDetails of filesToLoadThisIteration) {
            const currentProcessingPartName = loadDetails.originalTreatmentName; // e.g., "Inlay", "Crown", "Default"
            const modelFileBaseName = loadDetails.modelFileIdentifier; // e.g., "1_Inlay_Base", "1_Crown"
            const objectNameSuffix =
              loadDetails.modelFileIdentifierForObjectName; // e.g., "Inlay_Base", "Crown"

            const treatmentDetails = sortedTreatments.find((t) => {
              const { baseName } = parseTreatmentName(t.name);
              return (
                baseName.toLowerCase() ===
                currentProcessingPartName.toLowerCase()
              );
            });
            const rawModelName =
              treatmentDetails?.name ||
              treatmentDetails?.model_alias ||
              currentProcessingPartName;
            const { baseName: modelName, material: treatmentMaterial } =
              parseTreatmentName(rawModelName);

            // Check if this treatment has state-specific models (A for skull, B for jaw)
            const hasStateSpecificModel = checkForStateSpecificModel(modelName);

            let relativeModelPath;
            const state = currentView === "skull" ? "A" : "B";

            if (
              currentProcessingPartName.toLowerCase() === "sinus_drop" ||
              currentProcessingPartName.toLowerCase() === "sinus_lift"
            ) {
              let side;
              if (toothNumber >= 1 && toothNumber <= 4) {
                side = "R";
              } else if (toothNumber >= 13 && toothNumber <= 16) {
                side = "L";
              } else {
                side = toothNumber >= 9 && toothNumber <= 24 ? "L" : "R";
              }
              const type = "Drop";
              const fileName = `${side}MainSinus${type}_${state}.glb`;
              const folder = `Sinus_${state}`;
              relativeModelPath = `${folder}/${fileName}`;

              if (sinusModelRef.current[side]) {
                const existingModel = sinusModelRef.current[side];
                existingModel.traverse((part) => {
                  if (part.isMesh && part.morphTargetInfluences) {
                    let morphIndex = -1;
                    if (toothNumber >= 1 && toothNumber <= 4) {
                      morphIndex = toothNumber - 1;
                    } else if (toothNumber >= 13 && toothNumber <= 16) {
                      morphIndex = toothNumber - 13;
                    }

                    if (morphIndex !== -1) {
                      if (
                        currentProcessingPartName.toLowerCase() === "sinus_drop"
                      ) {
                        part.morphTargetInfluences[morphIndex] = 1;
                      } else {
                        part.morphTargetInfluences[morphIndex] = 0;
                      }
                    }
                  }
                });
                markToothLoaded(number);
                return;
              }
            } else if (currentProcessingPartName === "Default") {
              const folder =
                patientType === "CHILDREN" ? "child/Default" : "Default";
              // modelFileBaseName for Default is already toothIdInFilename
              relativeModelPath = `${folder}/${modelFileBaseName}.glb`;
            } else if (hasStateSpecificModel) {
              const stateSuffix = currentView === "skull" ? "_A" : "_B";
              const treatmentFolderWithState = `${modelName}${stateSuffix}`;
              const fileName = `${modelFileBaseName.replace(
                currentProcessingPartName,
                modelName,
              )}${stateSuffix}.glb`;
              const basePathPrefix =
                patientType === "CHILDREN"
                  ? `child/${treatmentFolderWithState}`
                  : treatmentFolderWithState;
              relativeModelPath = `${basePathPrefix}/${fileName}`;
            } else {
              // Regular treatment (including Inlay/Onlay parts)
              const folder =
                patientType === "CHILDREN" ? `child/${modelName}` : modelName;
              const fileName = `${modelFileBaseName.replace(
                currentProcessingPartName,
                modelName,
              )}.glb`;
              relativeModelPath = `${folder}/${fileName}`;
            }

            const modelPath = `${BASE_URL}/${TREATMENTS_VERSION}/${relativeModelPath}`;

            try {
              let gltf = modelCache.has(modelPath)
                ? {
                    scene: cloneModel(
                      modelCache.get(modelPath).scene,
                      currentView,
                    ),
                  }
                : await new Promise((resolve, reject) => {
                    loaderRef.current.load(
                      modelPath,
                      resolve,
                      undefined,
                      reject,
                    );
                  });

              if (!modelCache.has(modelPath)) {
                modelCache.set(modelPath, {
                  scene: cloneModel(gltf.scene, currentView),
                });

                // Debug: Log detailed animation track information
                if (gltf.animations?.length) {
                  gltf.animations.forEach((anim) => {
                    anim.tracks.forEach(() => {
                      // eslint-disable-next-line no-unused-vars
                    });
                  });

                  animationCache.set(
                    modelPath,
                    gltf.animations.map((a) => a.clone()),
                  );
                }
              }

              const cachedAnimations = animationCache.get(modelPath) || [];

              const animations = cachedAnimations.map((anim) =>
                sanitizeAnimationTracks(anim),
              );

              if (!mountedRef.current) return;

              // Debug: First log the original GLTF structure before any modifications

              // Log all nodes in the original GLTF
              const originalStructure = [];
              gltf.scene.traverse((obj) => {
                if (obj.name) {
                  originalStructure.push({
                    name: obj.name,
                    type: obj.type,
                    parent: obj.parent?.name || "scene",
                    isMesh: obj.isMesh,
                  });
                }
              });

              // Check if the GLTF has multiple root children
              if (gltf.scene.children.length > 1) {
                // Create a new group to contain all children
                const wrapperGroup = new THREE.Group();
                wrapperGroup.name = "wrapped_root";

                // Move all children to the wrapper group
                while (gltf.scene.children.length > 0) {
                  wrapperGroup.add(gltf.scene.children[0]);
                }

                // Add the wrapper group back to the scene
                gltf.scene.add(wrapperGroup);
              }

              const child = gltf.scene.children[0]; // This is the root of the loaded GLTF part
              if (!child) {
                return; // Skip this specific model file if it's empty
              }

              // Apply material AFTER child is defined
              if (treatmentMaterial) {
                applyMaterialByName(child, treatmentMaterial);
              }

              const originalRootName = child.name; // Capture original name, e.g., "11_Veneers_B"

              // Reset the root node's transform to neutralize any unwanted transform from the model file
              child.position.set(0, 0, 0);
              child.rotation.set(0, 0, 0);
              // child.scale.set(1, 1, 1);
              const newRootName = `tooth_${number}_${objectNameSuffix}_${currentView}`;
              child.name = newRootName; // Rename the root node

              child.userData = child.userData || {}; // Ensure userData exists
              child.userData.createdAt = Date.now();
              child.userData.viewType = currentView;

              // Debug: Log the structure of the loaded model after renaming

              // Log all child nodes in the loaded model
              const modelStructure = [];
              child.traverse((obj) => {
                if (obj.name) {
                  modelStructure.push({
                    name: obj.name,
                    type: obj.type,
                    parent: obj.parent?.name || "root",
                    isMesh: obj.isMesh,
                    hasAnimation: obj.animations?.length > 0,
                  });
                }
              });

              // First, collect all meshes
              const allMeshes = [];
              child.traverse((obj) => {
                if (obj.isMesh) {
                  allMeshes.push(obj);
                }
              });

              // Log available morph targets for debugging if needed
              if (
                currentProcessingPartName === "Decay" ||
                currentProcessingPartName === "Filling"
              ) {
                const morphTargetsFound = new Set();
                allMeshes.forEach((mesh) => {
                  if (mesh.morphTargetDictionary) {
                    Object.keys(mesh.morphTargetDictionary).forEach((key) => {
                      morphTargetsFound.add(key);
                    });
                  }
                });

                if (morphTargetsFound.size > 0) {
                  //
                }
              }

              // Setup meshes and apply morph targets
              child.traverse((part) => {
                if (part.isMesh) {
                  part.userData = part.userData || {};
                  // Don't store originalMaterial yet - we'll do it after all processing
                  part.userData.number = parseInt(number, 10);
                  part.userData.type = "tooth";
                  part.userData.isInteractive = true;
                  part.userData.viewType = currentView;

                  // Healthy tooth part visibility for "Default" model
                  if (currentProcessingPartName === "Default") {
                    if (
                      toothData.default_tooth &&
                      toothData.default_tooth_transparent
                    ) {
                      ensureAllMaterialsTransparent(child, 0.5);
                    }
                    if (part.isMesh) {
                      // Ensure we are only acting on the mesh
                      if (
                        !toothData.treatments ||
                        toothData.treatments.length === 0
                      ) {
                        part.visible = true;
                      } else {
                        // Determine visibility for crown and root separately
                        const crownVisible = determineHealthyPartVisibility(
                          toothData.treatments,
                          "crown",
                        );
                        const rootVisible = determineHealthyPartVisibility(
                          toothData.treatments,
                          "root",
                        );

                        // The whole healthy tooth part is visible if either its crown or root part is needed.
                        part.visible = crownVisible || rootVisible;
                      }

                      // Apply the Apictomy transparency override if needed
                      if (
                        toothData.treatments.some((t) => t.name === "Apictomy")
                      ) {
                        // If Apictomy is present, we ensure the tooth is visible and make it transparent.
                        part.visible = true;
                        if (part.material) {
                          const transparentMaterial = part.material.clone();
                          transparentMaterial.transparent = true;
                          transparentMaterial.opacity = 0.5;
                          transparentMaterial.depthWrite = false;
                          part.material = transparentMaterial;
                        }
                      }
                    }
                  }
                  // Make base tooth transparent for Onlay/Inlay
                  if (
                    objectNameSuffix === "Onlay_Base" ||
                    objectNameSuffix === "Inlay_Base"
                  ) {
                    if (part.material) {
                      const transparentMaterial = part.material.clone();
                      transparentMaterial.transparent = true;
                      transparentMaterial.opacity = 0.5;
                      transparentMaterial.depthWrite = false;
                      part.material = transparentMaterial;
                    }
                  }

                  // Only apply default properties to filling objects, coloring will be handled later

                  // Apply transparency for all treatments
                  if (toothData.treatments && toothData.treatments.length > 0) {
                    // Sort treatments by creation date (newest first)
                    const sortedTreatments = [...toothData.treatments].sort(
                      (a, b) => {
                        const dateA = new Date(a.created_at || 0);
                        const dateB = new Date(b.created_at || 0);
                        return dateB - dateA; // Newest first
                      },
                    );

                    // Find the treatment that corresponds to this part
                    const treatmentForPart = sortedTreatments.find(
                      (t) =>
                        t.name.toLowerCase() ===
                        currentProcessingPartName.toLowerCase(),
                    );

                    // If we have multiple treatments and this part corresponds to one of them
                    if (sortedTreatments.length > 1 && treatmentForPart) {
                      // Find the index of this treatment in the sorted array
                      const index = sortedTreatments.indexOf(treatmentForPart);

                      // Create a unique ID for the treatment
                      const baseId =
                        treatmentForPart.Id ||
                        treatmentForPart.id ||
                        `treatment_${index}`;
                      const treatmentId = `${baseId}_${index}`;

                      // Clone the material to avoid affecting other meshes
                      const treatmentMaterial = part.material.clone();

                      // Apply anti-z-fighting properties
                      part.material = applyAntiZFightingProperties(
                        treatmentMaterial,
                        index,
                        true, // Make it transparent
                      );
                      // Store the treatment information in the object's userData
                      part.userData.treatmentId = treatmentId; // Store for future reference
                      part.userData.baseId = baseId; // Store the original ID for reference
                      part.userData.treatmentIndex = index; // Store the index for reference
                      part.userData.treatmentName = treatmentForPart.name; // Store the name for reference

                      // Check if this treatment should be visible
                      const toothNumber = toothData.position_number || number;
                      const isVisible = getTreatmentVisibility(
                        toothNumber,
                        treatmentId,
                      );
                      part.visible = isVisible;

                      // Set render order based on index (higher index = rendered first)
                      part.renderOrder = 10 - index; // Negative so newer treatments render later
                    }
                  }

                  // Apply morph targets for decay and filling parts with exact matching
                  if (
                    part.morphTargetInfluences &&
                    part.morphTargetDictionary &&
                    toothData.treatments &&
                    toothData.treatments.length > 0 &&
                    (currentProcessingPartName === "Decay" ||
                      currentProcessingPartName === "Filling" ||
                      currentProcessingPartName === "Onlay")
                  ) {
                    const morphTargetNames = Object.keys(
                      part.morphTargetDictionary,
                    );

                    // Sort treatments by creation date (newest first)
                    const sortedTreatments = [...toothData.treatments].sort(
                      (a, b) => {
                        const dateA = new Date(a.created_at || 0);
                        const dateB = new Date(b.created_at || 0);
                        return dateB - dateA; // Newest first
                      },
                    );

                    // Process each treatment with different transparency levels
                    sortedTreatments.forEach((treatment) => {
                      // Skip treatments without surfaces
                      if (!treatment.surfaces) return;

                      // Process each surface in the treatment
                      Object.entries(treatment.surfaces).forEach(
                        ([surfaceName, surfaceData]) => {
                          const severity =
                            surfaceData.decaySeverity !== undefined
                              ? surfaceData.decaySeverity
                              : surfaceData.fillingSize;
                          if (severity !== undefined) {
                            // Use the imported findExactSurfaceMorphTarget function
                            const morphTarget = findExactSurfaceMorphTarget(
                              morphTargetNames,
                              surfaceName,
                              currentProcessingPartName, // Use currentProcessingPartName here
                            );

                            if (morphTarget) {
                              const targetIndex =
                                part.morphTargetDictionary[morphTarget];
                              part.morphTargetInfluences[targetIndex] =
                                severity;

                              // For filling parts, make them visible
                              if (currentProcessingPartName === "Filling") {
                                // Use currentProcessingPartName here
                                const fillingMaterial = part.material.clone();

                                // Set transparency properties
                                fillingMaterial.transparent = true;
                                fillingMaterial.opacity = 0.5; // 80% opacity as requested

                                // Apply anti-z-fighting properties using our utility function
                                // Don't set color here - it will be set later based on surface data
                                part.material = applyAntiZFightingProperties(
                                  fillingMaterial,
                                  1, // Index 0 for default treatment
                                  true, // Make it transparent
                                );

                                part.visible = true;
                                part.visible = true;
                              }
                            }
                          }
                        },
                      );
                    });
                  }

                  // Apply morph targets for Inlay and Onlay treatments
                  if (
                    toothData.treatments &&
                    toothData.treatments.length > 0 &&
                    (currentProcessingPartName.toLowerCase() === "inlay" ||
                      currentProcessingPartName.toLowerCase() === "onlay")
                  ) {
                    // Sort treatments by creation date (newest first)
                    const sortedTreatments = [...toothData.treatments].sort(
                      (a, b) => {
                        const dateA = new Date(a.created_at || 0);
                        const dateB = new Date(b.created_at || 0);
                        return dateB - dateA; // Newest first
                      },
                    );

                    // Find the treatment that matches Inlay or Onlay
                    const inlayOnlayTreatment = sortedTreatments.find(
                      (t) =>
                        t.name.toLowerCase() === "inlay" ||
                        t.name.toLowerCase() === "onlay",
                    );

                    if (inlayOnlayTreatment && inlayOnlayTreatment.surfaces) {
                      const treatmentSurfaces = Object.keys(
                        inlayOnlayTreatment.surfaces,
                      );
                      const treatmentType =
                        inlayOnlayTreatment.name.charAt(0).toUpperCase() +
                        inlayOnlayTreatment.name.slice(1).toLowerCase(); // Normalize to "Inlay" or "Onlay"

                      // Check if this is an individual surface part (e.g., Onlay_1_Distal, Inlay_1_Mesial)
                      const individualSurfacePattern = new RegExp(
                        `^${treatmentType}_${number}_([A-Za-z]+)$`,
                      );
                      const individualSurfaceMatch =
                        part.name && part.name.match(individualSurfacePattern);

                      if (individualSurfaceMatch) {
                        // This is an individual surface part
                        const surfaceName = individualSurfaceMatch[1];

                        // Check if this surface is in the treatment surfaces array
                        const surfaceInTreatment = treatmentSurfaces.some(
                          (s) => s.toLowerCase() === surfaceName.toLowerCase(),
                        );

                        if (surfaceInTreatment) {
                          // Show the mesh and set its morph target to 1
                          part.visible = true;

                          // Find and apply the morph target
                          if (
                            part.morphTargetDictionary &&
                            part.morphTargetInfluences
                          ) {
                            Object.keys(part.morphTargetDictionary).forEach(
                              (morphName) => {
                                // Look for morph target pattern like Morpher_ncl1_XX.OnlayTarget_{toothNumber}_{surface}
                                const morphPattern = new RegExp(
                                  `Morpher_ncl1_\\d+\\.${treatmentType}Target_${number}_${surfaceName}`,
                                  "i",
                                );

                                if (morphPattern.test(morphName)) {
                                  const morphIndex =
                                    part.morphTargetDictionary[morphName];
                                  if (
                                    morphIndex !== undefined &&
                                    part.morphTargetInfluences[morphIndex] !==
                                      undefined
                                  ) {
                                    part.morphTargetInfluences[morphIndex] = 1;
                                  }
                                }
                              },
                            );
                          }
                        } else {
                          // Hide the mesh if surface is not in treatment
                          part.visible = false;
                        }
                      }

                      // Check if this is a combined tooth part (e.g., OnlayTooth_1_Base, InlayTooth_1_Base)
                      const combinedToothPattern = new RegExp(
                        `^${treatmentType}Tooth_${number}_Base$`,
                      );
                      const isCombinedTooth =
                        part.name && combinedToothPattern.test(part.name);

                      if (
                        isCombinedTooth &&
                        part.morphTargetDictionary &&
                        part.morphTargetInfluences
                      ) {
                        // This is a combined tooth part with multiple morph targets
                        // Apply morph targets for each surface in the treatment
                        treatmentSurfaces.forEach((surfaceName) => {
                          Object.keys(part.morphTargetDictionary).forEach(
                            (morphName) => {
                              // Look for morph target pattern like Morpher_ncl1_XX.OnlayTooth_{toothNumber}_{surface}
                              const morphPattern = new RegExp(
                                `Morpher_ncl1_\\d+\\.${treatmentType}Tooth_${number}_${surfaceName}`,
                                "i",
                              );

                              if (morphPattern.test(morphName)) {
                                const morphIndex =
                                  part.morphTargetDictionary[morphName];
                                if (
                                  morphIndex !== undefined &&
                                  part.morphTargetInfluences[morphIndex] !==
                                    undefined
                                ) {
                                  part.morphTargetInfluences[morphIndex] = 1;
                                }
                              }
                            },
                          );
                        });
                      }
                    }
                  }
                  // Handle Sinus Drop/Lift morph targets
                  if (
                    part.isMesh &&
                    part.morphTargetInfluences &&
                    (currentProcessingPartName.toLowerCase() === "sinus_drop" ||
                      currentProcessingPartName.toLowerCase() === "sinus_lift")
                  ) {
                    // First, reset all influences to 0
                    for (
                      let i = 0;
                      i < part.morphTargetInfluences.length;
                      i++
                    ) {
                      part.morphTargetInfluences[i] = 0;
                    }

                    let morphIndex = -1;
                    const toothNum = parseInt(number, 10);

                    // Calculate the dynamic index based on tooth number
                    if (toothNum >= 1 && toothNum <= 4) {
                      morphIndex = toothNum - 1;
                    } else if (toothNum >= 13 && toothNum <= 16) {
                      morphIndex = toothNum - 13;
                    }

                    // Determine the value based on treatment name
                    if (morphIndex !== -1) {
                      if (
                        currentProcessingPartName.toLowerCase() === "sinus_drop"
                      ) {
                        if (
                          part.morphTargetInfluences[morphIndex] !== undefined
                        ) {
                          part.morphTargetInfluences[morphIndex] = 1;
                        }
                      } else {
                        if (
                          part.morphTargetInfluences[morphIndex] !== undefined
                        ) {
                          part.morphTargetInfluences[morphIndex] = 0;
                        }
                      }
                      // For 'sinus_drop', the value remains 0, which was set in the reset loop.
                    }
                  }
                  if (
                    (currentProcessingPartName.toLowerCase() === "sinus_drop" ||
                      currentProcessingPartName.toLowerCase() ===
                        "sinus_lift") &&
                    child
                  ) {
                    if (part.material) {
                      const transparentMaterial = part.material.clone();
                      transparentMaterial.transparent = true;
                      transparentMaterial.opacity = 0.5;
                      transparentMaterial.depthWrite = false;
                      part.material = transparentMaterial;
                    }
                    let side = toothNumber >= 1 && toothNumber <= 4 ? "R" : "L";
                    sinusModelRef.current[side] = child;
                  }
                  part.material.side = THREE.DoubleSide;
                  part.castShadow = true;
                  part.receiveShadow = true;

                  if (
                    currentProcessingPartName === "BoneGraftingBlock" &&
                    part.name.includes("BoneGrafting")
                  ) {
                    part.material = BoneGraftingBlockMaterial();
                  }
                  if (currentProcessingPartName === "Apictomy") {
                    if (part.material.name === "Teeth") {
                      const transparentMaterial = part.material.clone();
                      transparentMaterial.transparent = true;
                      transparentMaterial.opacity = 0.5;
                      transparentMaterial.depthWrite = false;
                      part.material = transparentMaterial;
                    }
                  }
                }

                if (part.isMesh) {
                  // Roo: Add check to ensure logging only happens for meshes
                  // Log final material properties
                  if (!part.material) {
                    // This check is now somewhat redundant if obj.isMesh, but kept for robustness
                  }
                } // Roo: Closes if (obj.isMesh) for logging
              });

              // Apply material coloring for exact material name matches - Decay
              if (
                currentProcessingPartName === "Decay" && // Use currentProcessingPartName here
                toothData.treatments &&
                toothData.treatments[0] &&
                toothData.treatments[0].surfaces
              ) {
                Object.entries(toothData.treatments[0].surfaces).forEach(
                  ([surfaceName, surfaceData]) => {
                    if (surfaceData.decaySeverity > 0) {
                      // The specific material name we want to match exactly
                      const targetMaterialName = `decay_${surfaceName.toLowerCase()}`;

                      // Look through all meshes for exact match
                      allMeshes.forEach((mesh) => {
                        mesh.visbile = false;
                        const materialName = (
                          mesh.material?.name || ""
                        ).toLowerCase();

                        // Only color if the material name is EXACTLY "decay_[surfaceName]"
                        if (materialName.includes(targetMaterialName)) {
                          mesh.material.color.set(0x000000);
                          mesh.material.side = THREE.DoubleSide; // Turn off backface culling to prevent clipping
                          //
                        }
                      });
                    }
                  },
                );
              }

              // Apply material coloring for exact material name matches - Filling
              if (
                currentProcessingPartName === "Filling" &&
                decaySurfaces.length > 0
              ) {
                // Use currentProcessingPartName here
                // First, hide all filling surface meshes by default (but not the main tooth mesh)
                allMeshes.forEach((mesh) => {
                  const meshName = mesh.name || "";
                  // Only hide specific filling surface meshes, not the main tooth mesh
                  if (
                    meshName.startsWith(`Filling_${number}_`) &&
                    meshName.endsWith("_Base")
                  ) {
                    mesh.visible = false;
                  }
                });

                decaySurfaces.forEach((surfaceName) => {
                  const surfaceData =
                    toothData.treatments[0].surfaces[surfaceName];

                  if (surfaceData && surfaceData.decaySeverity > 0) {
                    // Look through all meshes for pattern match
                    allMeshes.forEach((mesh) => {
                      const meshName = mesh.name || "";

                      // Check if mesh name matches the pattern: Filling_[number]_[SurfaceName]_Base
                      // Also handle common naming variations and compound surfaces
                      const surfaceVariations = [
                        surfaceName,
                        // Handle Palatal/Lingual variations
                        surfaceName.replace("Palatal", "Lingual"),
                        surfaceName.replace("Lingual", "Palatal"),
                        // Handle compound surfaces like DistalOcclusal -> split into Distal + Occlusal
                        ...(surfaceName.includes("Occlusal")
                          ? [surfaceName.replace("Occlusal", ""), "Occlusal"]
                          : []),
                        // Handle other compound surfaces
                        ...(surfaceName.includes("Buccal")
                          ? [surfaceName.replace("Buccal", ""), "Buccal"]
                          : []),
                        ...(surfaceName.includes("Lingual")
                          ? [surfaceName.replace("Lingual", ""), "Lingual"]
                          : []),
                        ...(surfaceName.includes("Palatal")
                          ? [surfaceName.replace("Palatal", ""), "Palatal"]
                          : []),
                      ].filter((v) => v && v.length > 0); // Remove empty strings

                      const matchFound = surfaceVariations.some((variation) =>
                        meshName.includes(`Filling_${number}_${variation}_`),
                      );

                      // Also check if this is the main tooth mesh and has morph targets for this surface
                      const isMainToothMesh = meshName.includes(
                        `tooth_${number}_Filling_`,
                      );
                      const hasDirectMorphTarget =
                        isMainToothMesh &&
                        mesh.morphTargetDictionary &&
                        Object.keys(mesh.morphTargetDictionary).some(
                          (morphName) =>
                            morphName.includes(
                              `Filling_${number}_${surfaceName}`,
                            ),
                        );

                      if (matchFound || hasDirectMorphTarget) {
                        // Make this surface visible and yellow
                        mesh.visible = true;
                        mesh.material.color.set(0xffff00); // Yellow
                        mesh.material.emissive.set(0x333300); // Slight yellow glow
                        mesh.material.side = THREE.DoubleSide; // Turn off backface culling to prevent clipping

                        // Apply morph targets if available
                        if (
                          mesh.morphTargetDictionary &&
                          mesh.morphTargetInfluences
                        ) {
                          // Look for morph targets that match this surface
                          Object.keys(mesh.morphTargetDictionary).forEach(
                            (morphName) => {
                              if (
                                morphName.includes(
                                  `Filling_${number}_${surfaceName}`,
                                )
                              ) {
                                const morphIndex =
                                  mesh.morphTargetDictionary[morphName];
                                if (
                                  morphIndex !== undefined &&
                                  mesh.morphTargetInfluences[morphIndex] !==
                                    undefined
                                ) {
                                  mesh.morphTargetInfluences[morphIndex] =
                                    surfaceData.decaySeverity;

                                  // Store this morph target setting to preserve during animations
                                  const meshKey = `${number}_${mesh.uuid}`;
                                  if (
                                    !fillingMorphTargetsRef.current.has(meshKey)
                                  ) {
                                    fillingMorphTargetsRef.current.set(
                                      meshKey,
                                      new Map(),
                                    );
                                  }
                                  fillingMorphTargetsRef.current
                                    .get(meshKey)
                                    .set(morphIndex, surfaceData.decaySeverity);
                                }
                              }
                            },
                          );
                        }
                      }
                    });
                  }
                });
              }

              // Get the treatment name for various operations (remains based on the primary treatment of the tooth)
              const firstTreatmentNameForTooth = // Renamed for clarity from 'treatmentName'
                toothData.treatments && toothData.treatments.length > 0
                  ? toothData.treatments[0].name
                  : "Default";

              // Set the primary model. If a default model is loaded, it should be primary.
              // Otherwise, the first treatment model becomes primary.
              if (currentProcessingPartName === "Default") {
                primaryModel = child;
              } else if (!primaryModel) {
                primaryModel = child;
              }

              if (number >= 9 && number <= 24) {
                if (currentView === "skull") {
                  if (
                    firstTreatmentNameForTooth
                      .toLowerCase()
                      .includes("sinus_drop") ||
                    firstTreatmentNameForTooth
                      .toLowerCase()
                      .includes("sinus_lift")
                  ) {
                    child.scale.set(
                      child.scale.x,
                      child.scale.y,
                      child.scale.z,
                    );
                  }
                  if (
                    !(
                      firstTreatmentNameForTooth
                        .toLowerCase()
                        .includes("denture") ||
                      firstTreatmentNameForTooth
                        .toLowerCase()
                        .includes("zygomaticimplant") ||
                      firstTreatmentNameForTooth
                        .toLowerCase()
                        .includes("sinus_drop") ||
                      firstTreatmentNameForTooth
                        .toLowerCase()
                        .includes("sinus_lift") ||
                      firstTreatmentNameForTooth
                        .toLowerCase()
                        .includes("maryland")
                    )
                  ) {
                    child.scale.set(
                      -child.scale.x,
                      child.scale.y,
                      -child.scale.z,
                    );
                  }
                } else if (currentView === "jaw") {
                  if (
                    firstTreatmentNameForTooth
                      .toLowerCase()
                      .includes("sinus_drop") ||
                    firstTreatmentNameForTooth
                      .toLowerCase()
                      .includes("sinus_lift")
                  ) {
                    child.scale.set(
                      -child.scale.x,
                      child.scale.y,
                      -child.scale.z,
                    );
                  }
                  if (
                    !(
                      firstTreatmentNameForTooth
                        .toLowerCase()
                        .includes("denture") ||
                      firstTreatmentNameForTooth
                        .toLowerCase()
                        .includes("zygomaticimplant") ||
                      firstTreatmentNameForTooth
                        .toLowerCase()
                        .includes("sinus_drop") ||
                      firstTreatmentNameForTooth
                        .toLowerCase()
                        .includes("sinus_lift")
                    )
                  ) {
                    child.scale.set(
                      child.scale.x,
                      -child.scale.y,
                      child.scale.z,
                    );

                    if (patientType === "CHILDREN") {
                      child.scale.set(
                        -child.scale.x,
                        -child.scale.y,
                        -child.scale.z,
                      );
                    }
                  }
                } else if (currentView === "charting") {
                  const originalScale = new Vector3();
                  child.getWorldScale(originalScale);
                  child.scale.set(1, 1, 1);
                  child.updateMatrixWorld(true);
                  child.scale.set(
                    -Math.abs(originalScale.x),
                    -Math.abs(originalScale.y),
                    -Math.abs(originalScale.z),
                  );
                }
              }

              // Set rotation for Decay or Filling treatments
              // Use firstTreatmentNameForTooth as this rotation is specific to the overall treatment type
              if (
                firstTreatmentNameForTooth.toLowerCase() === "decay" ||
                firstTreatmentNameForTooth.toLowerCase() === "filling" ||
                firstTreatmentNameForTooth.toLowerCase() === "onlay" ||
                firstTreatmentNameForTooth
                  .toLowerCase()
                  .includes("sinus_drop") ||
                firstTreatmentNameForTooth.toLowerCase().includes("sinus_lift")
              ) {
                child.rotation.set(-89.5, 0, 0);
              }
              if (
                number >= 9 &&
                number <= 24 &&
                firstTreatmentNameForTooth === "Onlay"
              ) {
                child.rotation.set(-89.5, 0, 0);
                child.scale.set(child.scale.x, -child.scale.y, -child.scale.z);
              }
              if (
                number >= 9 &&
                number <= 24 &&
                firstTreatmentNameForTooth === "Filling"
                //   ||
                // firstTreatmentNameForTooth === "Decay"
              ) {
                child.scale.set(child.scale.x, -child.scale.y, -child.scale.z);
              }
              if (
                number >= 9 &&
                number <= 24 &&
                firstTreatmentNameForTooth === "Decay"
                //   ||
                // firstTreatmentNameForTooth === "Decay"
              ) {
                child.scale.set(child.scale.x, -child.scale.y, -child.scale.z);
              }
              // Store originalMaterial AFTER all material processing is complete
              child.traverse((obj) => {
                if (obj.isMesh && !obj.userData.originalMaterial) {
                  obj.userData.originalMaterial = obj.material.clone();
                }
              });

              // Check if the CURRENT treatment (not just the first one) needs FullDentureState pointer
              if (
                currentProcessingPartName.toLowerCase().includes("denture") ||
                currentProcessingPartName
                  .toLowerCase()
                  .includes("zygomaticimplant") ||
                currentProcessingPartName
                  .toLowerCase()
                  .includes("sinus_drop") ||
                currentProcessingPartName.toLowerCase().includes("sinus_lift")
              ) {
                let fullDenturePointer;
                if (currentView === "jaw") {
                  fullDenturePointer =
                    pointersRef.current.get("FullDentureStateB");
                } else {
                  fullDenturePointer =
                    pointersRef.current.get("FullDentureStateA");
                }
                // if( currentProcessingPartName.includes("sinus_drop")
                //       || currentProcessingPartName.includes("sinus_lift")){
                //                     // child.scale.set(child.scale.x, -child.scale.y, child.scale.z);

                //     }
                fullDenturePointer.add(child);
              } else {
                // Check if pointer is in the scene hierarchy
                let parent = pointer;
                let depth = 0;
                while (parent && depth < 10) {
                  parent = parent.parent;
                  depth++;
                }

                pointer.add(child);

                const worldPos = new THREE.Vector3();
                child.getWorldPosition(worldPos);

                // Check material properties
                child.traverse((obj) => {
                  if (obj.isMesh) {
                    if (obj.material.transparent) {
                      //
                    }
                  }
                });
              }
              if (animations.length) {
                // Pass the 'child' (the actual object being added to the scene and animated),
                // its original root name, and its new root name.
                processAnimations(
                  parseInt(number, 10),
                  animations,
                  child, // The root object for this animation set (already renamed)
                  currentProcessingPartName,
                  originalRootName, // Original name of child before renaming
                  newRootName, // New name of child after renaming
                );
              }
            } catch {
              // eslint-disable-line no-unused-vars
              // console.error(
              //   `[SkullJawTeeth] Error loading tooth model for part: ${modelFileBaseName}`,
              //   {
              //     error,
              //     modelPath,
              //     toothNumber: number,
              //     treatmentName: currentProcessingPartName,
              //   },
              // );
            }
          } // End of inner loop (filesToLoadThisIteration)
        } // End of outer loop (modelParts)

        if (primaryModel) {
          internalTeethRef.current.set(parseInt(number, 10), primaryModel);

          if (teethRef && teethRef.current) {
            teethRef.current.set(parseInt(number, 10), primaryModel);
          }
        }

        markToothLoaded(number);
      },
      [
        mountedRef,
        internalTeethRef,
        teethRef,
        currentView,
        processAnimations,
        markToothLoaded,
        patientType,
        getTreatmentVisibility,
      ],
    );

    const validateScene = () => {
      if (!pointersRef?.current) return;

      const allMeshes = [];
      const duplicates = new Map();
      pointersRef.current.forEach((pointer, toothNumber) => {
        pointer.traverse((obj) => {
          if (obj.isMesh) {
            const meshInfo = {
              name: obj.name,
              uuid: obj.uuid,
              tooth: toothNumber,
              parent: obj.parent?.name,
            };

            if (allMeshes.some((m) => m.uuid === obj.uuid)) {
              duplicates.set(obj.uuid, meshInfo);
            }
            allMeshes.push(meshInfo);
          }
        });
      });
    };

    useEffect(() => {
      if (
        loadingTeeth.size > 0 &&
        loadingTeeth.size === totalTeethToLoadRef.current &&
        isLoading
      ) {
        setIsLoading(false);
      }
    }, [loadingTeeth, isLoading, currentView]);

    useEffect(() => {
      mountedRef.current = true;

      const teethKey = Object.keys(patientTeeth).sort().join(",");
      const instanceKey = `${currentView}-${teethKey}`;

      if (isInitializedRef.current === instanceKey) {
        return;
      }

      // Always clean up existing teeth first
      cleanupTeeth();

      // Update the instance key
      isInitializedRef.current = instanceKey;

      // Reset loading state
      setLoadingTeeth(new Map());

      // Only load new teeth if there are any
      if (pointersRef?.current && Object.keys(patientTeeth).length > 0) {
        // Check each pointer's status
        pointersRef.current.forEach(() => {});
        // Check for full ChromePartialDenture condition
        const teethNumbers = Array.from({ length: 16 }, (_, i) => i + 1);
        const hasFullChromePartialDenture = teethNumbers.every((num) => {
          const tooth = patientTeeth[num];
          return (
            tooth &&
            tooth.treatments &&
            tooth.treatments.some((t) => t.name === "ChromePartialDenture")
          );
        });

        if (hasFullChromePartialDenture) {
          const loadMetalTop = async () => {
            const state = currentView === "skull" ? "A" : "B";
            const modelPath = `https://upod.s3.eu-central-1.amazonaws.com/treatmentsV4/ChromePartialDenture_${state}/MetalTop_ChromePartialDenture_${state}.glb`;
            try {
              let gltf;
              if (modelCache.has(modelPath)) {
                gltf = {
                  scene: cloneModel(
                    modelCache.get(modelPath).scene,
                    currentView,
                  ),
                };
              } else {
                gltf = await loaderRef.current.loadAsync(modelPath);
                modelCache.set(modelPath, {
                  scene: cloneModel(gltf.scene, currentView),
                });
              }

              if (gltf.scene) {
                const metalTopModel = gltf.scene.children[0];
                // metalTopModel.name = `metal_top_chrome_partial_denture_${state}`;
                const fullDenturePointer = pointersRef.current.get(
                  `FullDentureState${state}`,
                );
                if (fullDenturePointer) {
                  metalTopModel.position.set(0, 0, 0);
                  fullDenturePointer.add(metalTopModel);
                }
              }
            } catch (error) {
              console.error("Error loading MetalTop model:", error);
            }
          };
          loadMetalTop();
        }

        const teethToLoad = Object.keys(patientTeeth);
        totalTeethToLoadRef.current = teethToLoad.length;

        setIsLoading(true);

        // Add a small delay between loading each tooth to prevent overwhelming the GPU
        teethToLoad.forEach((number, index) => {
          setTimeout(() => {
            if (!mountedRef.current) return;
            const pointer = pointersRef.current?.get(parseInt(number, 10));
            if (pointer) {
              const originalToothData = patientTeeth[number];
              const normalizedToothData = {
                ...originalToothData,
                treatments: originalToothData.treatments
                  ? originalToothData.treatments.map((t) => ({
                      ...t,
                      name: t.name === "Inlay" ? "Onlay" : t.name,
                    }))
                  : [],
              };
              loadToothModel(number, normalizedToothData, pointer);
            }
          }, index * 50); // 50ms delay between each tooth
        });
      } else {
        // If there are no teeth to load, make sure we're not in loading state
        totalTeethToLoadRef.current = 0;
        setIsLoading(false);
      }

      return () => {
        mountedRef.current = false;
      };
    }, [patientTeeth, pointersRef, currentView, cleanupTeeth, loadToothModel]);

    React.useImperativeHandle(ref, () => ({
      playAnimations,
      pauseAnimations,
      resetAnimations,
      validateScene,
      isLoading: () => isLoading,
      loadedTeethCount: () => loadingTeeth.size,
      totalTeethCount: () => totalTeethToLoadRef.current,
      // Add setToothMorphTargets for backward compatibility
      setToothMorphTargets: () => {
        // This function is kept for backward compatibility
        // The component now handles morph targets internally based on the patientTeeth data
        // No action needed as morph targets are now applied directly in loadToothModel
        return true;
      },
    }));

    return null;
  },
);

SkullJawTeeth.displayName = "SkullJawTeeth";
export default SkullJawTeeth;
