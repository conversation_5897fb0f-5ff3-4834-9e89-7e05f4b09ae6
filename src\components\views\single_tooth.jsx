import { useRef, useEffect, useState, useCallback } from "react"; // Removed React
import { useThree } from "@react-three/fiber"; // Removed useFrame
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import * as THREE from "three";
import { useTeeth } from "../../context/TeethContext";
import { getModelUrl } from "../../constants/models";
import {
  modelCache,
  animationCache,
  createYPositionOnlyClip,
  getModelParts,
  cloneModel,
  findExactSurfaceMorphTarget,
  forceSceneUpdate,
  // clearAllTeethFromScene, // Removed unused import
  // checkForStateSpecificModel, // Not directly used here, getModelUrl handles state logic
} from "../../utils/modelUtils";
import { determineHealthyPartVisibility } from "../../utils/treatmentUtils";
import { applyAntiZFightingProperties } from "../../utils/materialUtils";
import { ensureAllMaterialsTransparent } from "../../utils/transparencyUtils"; // Removed unused import
import { Html } from "@react-three/drei";
import TreatmentsListWrapper from "../UI/TreatmentsListWrapper";

// Helper to find treatment index (can be shared or kept local if specific adjustments are needed)
const findTreatmentIndex = (
  treatments,
  treatment,
  partName = null,
  meshName = null,
) => {
  if (meshName) {
    const meshParts = meshName.split("_");
    if (meshParts.length >= 3) {
      const meshTreatmentName = meshParts[2];
      const exactNameIndex = treatments.findIndex(
        (t) => t.name.toLowerCase() === meshTreatmentName.toLowerCase(),
      );
      if (exactNameIndex >= 0) return exactNameIndex;
    }
  }
  for (let idx = 0; idx < treatments.length; idx++) {
    const t = treatments[idx];
    if (
      (treatment.Id && t.Id === treatment.Id) ||
      (treatment.id && t.id === treatment.id)
    ) {
      if (t.name.toLowerCase() === treatment.name.toLowerCase()) return idx;
    }
  }
  for (let idx = 0; idx < treatments.length; idx++) {
    const t = treatments[idx];
    if (t.name.toLowerCase() === treatment.name.toLowerCase()) return idx;
  }
  if (partName) {
    for (let idx = 0; idx < treatments.length; idx++) {
      const t = treatments[idx];
      if (t.name.toLowerCase() === partName.toLowerCase()) return idx;
    }
  }
  return -1;
};

export const SingleTooth = ({ toothNumberToDisplay }) => {
  const {
    patientTeeth,
    getPatientType,
    getTreatmentVisibility,
    toggleTreatmentVisibility,
    treatmentVisibility,
  } = useTeeth();

  const patientType = getPatientType();
  const [currentToothData, setCurrentToothData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const toothContainerRef = useRef(new THREE.Group()); // A group to hold all parts of the tooth
  const loaderRef = useRef(new GLTFLoader());
  const mountedRef = useRef(true);
  const mixersRef = useRef(new Map()); // For animations, if any
  const actionsRef = useRef(new Map()); // For animations, if any

  const { scene, gl: renderer, camera } = useThree();

  const cleanupToothModels = useCallback(() => {
    mixersRef.current.forEach((mixer) => mixer.stopAllAction());
    mixersRef.current.clear();
    actionsRef.current.clear();

    if (toothContainerRef.current) {
      while (toothContainerRef.current.children.length > 0) {
        const child = toothContainerRef.current.children[0];
        toothContainerRef.current.remove(child);
        child.traverse((obj) => {
          if (obj.isMesh) {
            obj.geometry?.dispose();
            if (Array.isArray(obj.material)) {
              obj.material.forEach((m) => m?.dispose());
            } else {
              obj.material?.dispose();
            }
          }
        });
      }
    }
    if (renderer && scene && camera) {
      forceSceneUpdate(renderer, scene, camera);
    }
  }, [scene, renderer, camera]);

  useEffect(() => {
    mountedRef.current = true;
    loaderRef.current.setCrossOrigin("anonymous");
    scene.add(toothContainerRef.current); // Add the main container to the scene once

    return () => {
      mountedRef.current = false;
      cleanupToothModels();
      if (toothContainerRef.current.parent) {
        toothContainerRef.current.parent.remove(toothContainerRef.current);
      }
    };
  }, [cleanupToothModels, scene]);

  const processAnimations = useCallback(
    (toothNumber, animations, parentObject, treatmentName) => {
      if (!animations || animations.length === 0) return;
      let mixer = mixersRef.current.get(toothNumber);
      if (!mixer) {
        mixer = new THREE.AnimationMixer(parentObject);
        mixersRef.current.set(toothNumber, mixer);
      }
      animations.forEach((clip) => {
        const validTracks = clip.tracks.filter((track) =>
          parentObject.getObjectByName(track.name.split(".")[0]),
        );
        if (validTracks.length === 0) return;
        const validClip = new THREE.AnimationClip(
          clip.name,
          clip.duration,
          validTracks,
        );
        const actionKey = `${toothNumber}_${treatmentName}_${validClip.name}`;
        if (!actionsRef.current.has(actionKey)) {
          const action = mixer.clipAction(validClip);
          action.setLoop(THREE.LoopOnce);
          action.clampWhenFinished = true;
          actionsRef.current.set(actionKey, action);
        }
      });
    },
    [],
  );

  const loadToothModel = useCallback(
    async (toothData, toothNumber) => {
      if (!mountedRef.current || !toothData) {
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      cleanupToothModels(); // Clean up previous models before loading new ones

      const hasMissingToothTreatment = toothData.treatments?.some(
        (t) => t.missing_tooth_indicator,
      );
      if (toothData.marked_as_missing || hasMissingToothTreatment) {
        setIsLoading(false);
        return;
      }

      const sortedTreatments = toothData.treatments
        ? [...toothData.treatments].sort(
            (a, b) => new Date(b.created_at || 0) - new Date(a.created_at || 0),
          )
        : [];

      const modelParts = getModelParts(toothData);
      // let primaryModelForRef = null; // If needed for a specific ref later

      const toothIdInFilename =
        patientType === "CHILDREN" ? `${toothNumber}C` : `${toothNumber}`;

      for (const basePartName of modelParts) {
        let filesToLoadThisIteration = [];

        if (basePartName === "Inlay") {
          filesToLoadThisIteration.push({
            originalTreatmentName: basePartName,
            modelFileIdentifier: `${toothIdInFilename}_Inlay_Base`,
            modelFileIdentifierForObjectName: "Inlay_Base",
          });
          filesToLoadThisIteration.push({
            originalTreatmentName: basePartName,
            modelFileIdentifier: `${toothIdInFilename}_InlayTooth_Base`,
            modelFileIdentifierForObjectName: "InlayTooth_Base",
          });
        } else if (basePartName === "Onlay") {
          filesToLoadThisIteration.push({
            originalTreatmentName: basePartName,
            modelFileIdentifier: `${toothIdInFilename}_Onlay_Base`,
            modelFileIdentifierForObjectName: "Onlay_Base",
          });
          filesToLoadThisIteration.push({
            originalTreatmentName: basePartName,
            modelFileIdentifier: `${toothIdInFilename}_OnlayTooth_Base`,
            modelFileIdentifierForObjectName: "OnlayTooth_Base",
          });
        } else {
          const modelFileId =
            basePartName === "Default"
              ? toothIdInFilename
              : `${toothIdInFilename}_${basePartName}`;
          filesToLoadThisIteration.push({
            originalTreatmentName: basePartName,
            modelFileIdentifier: modelFileId,
            modelFileIdentifierForObjectName: basePartName,
          });
        }

        for (const loadDetails of filesToLoadThisIteration) {
          const currentProcessingPartName = loadDetails.originalTreatmentName;
          const modelFileBaseNameForURL = loadDetails.modelFileIdentifier;
          const objectNameSuffix = loadDetails.modelFileIdentifierForObjectName;

          // For single_tooth view, assume 'STATE_A' (skull-like view) for getModelUrl if state is relevant
          // However, for tooth-specific files like Inlay/Onlay, getModelUrl handles it directly.
          // For Default or other treatments, it might need a view context.
          // Let's assume a generic view context or that getModelUrl handles it.
          const stateKeyForModelUrl = "STATE_A"; // Or determine based on actual view context if needed
          const modelPath = getModelUrl(
            modelFileBaseNameForURL,
            stateKeyForModelUrl,
            patientType,
          );

          if (!modelPath) {
            console.warn(
              `[SingleTooth] Could not determine model path for ${modelFileBaseNameForURL}`,
            );
            continue;
          }

          try {
            let gltf;
            if (modelCache.has(modelPath)) {
              gltf = {
                scene: cloneModel(
                  modelCache.get(modelPath).scene,
                  "single_tooth_view",
                ),
              }; // Unique view key for cache
            } else {
              gltf = await new Promise((resolve, reject) =>
                loaderRef.current.load(modelPath, resolve, undefined, reject),
              );
            }

            if (!modelCache.has(modelPath) && gltf.scene) {
              modelCache.set(modelPath, {
                scene: cloneModel(gltf.scene, "single_tooth_view"),
              });
              if (gltf.animations?.length) {
                animationCache.set(
                  modelPath,
                  gltf.animations.map((a) => a.clone()),
                );
              }
            }

            if (!mountedRef.current) return;
            if (
              !gltf.scene ||
              !gltf.scene.children ||
              gltf.scene.children.length === 0
            ) {
              console.warn(
                `[SingleTooth] GLTF scene is empty or invalid for ${modelPath}`,
              );
              continue;
            }

            const child = gltf.scene.children[0];
            child.name = `tooth_${toothNumber}_${objectNameSuffix}_single_view`;
            child.userData = {
              ...child.userData,
              createdAt: Date.now(),
              viewType: "single_tooth_view",
            };

            child.traverse((obj) => {
              if (obj.isMesh) {
                obj.userData = {
                  ...obj.userData,
                  number: parseInt(toothNumber, 10),
                  type: "tooth",
                  isInteractive: true,
                  viewType: "single_tooth_view",
                };

                if (currentProcessingPartName === "Default") {
                  if (
                    toothData.default_tooth &&
                    toothData.default_tooth_transparent
                  ) {
                    ensureAllMaterialsTransparent(child, 0.5);
                  }
                  if (obj.name === "Root" || obj.name === "root") {
                    obj.visible = determineHealthyPartVisibility(
                      toothData.treatments,
                      "root",
                    );
                  } else if (obj.name === "Crown" || obj.name === "crown") {
                    obj.visible = determineHealthyPartVisibility(
                      toothData.treatments,
                      "crown",
                    );
                  }
                }

                // Transparency and Anti-Z fighting for multiple treatments
                const treatmentForPart = sortedTreatments.find(
                  (t) => t.name === currentProcessingPartName,
                );
                if (sortedTreatments.length > 0 && treatmentForPart) {
                  // Changed from > 1 to > 0 to apply to all treatments
                  const index = sortedTreatments.indexOf(treatmentForPart);
                  const baseId =
                    treatmentForPart.Id ||
                    treatmentForPart.id ||
                    `treatment_${index}`;
                  const actualTreatmentIndex = findTreatmentIndex(
                    toothData.treatments,
                    treatmentForPart,
                    currentProcessingPartName,
                    obj.name,
                  );
                  const finalIndex =
                    actualTreatmentIndex >= 0 ? actualTreatmentIndex : index;
                  const treatmentId = `${baseId}_${finalIndex}`;

                  obj.userData.treatmentId = treatmentId;
                  obj.userData.baseId = baseId;
                  obj.userData.treatmentIndex = finalIndex;
                  obj.userData.treatmentName = treatmentForPart.name;

                  obj.material = applyAntiZFightingProperties(
                    obj.material.clone(),
                    finalIndex,
                    true,
                  );
                  obj.visible = getTreatmentVisibility(
                    toothNumber,
                    treatmentId,
                  );
                  obj.renderOrder = -finalIndex;
                } else if (
                  sortedTreatments.length === 0 &&
                  currentProcessingPartName === "Default"
                ) {
                  // Ensure default tooth is fully opaque if no treatments
                  if (obj.material.isMeshStandardMaterial) {
                    const newMat = obj.material.clone();
                    newMat.transparent = false;
                    newMat.opacity = 1;
                    obj.material = newMat;
                  }
                }

                // Morph targets for Decay/Filling
                if (
                  obj.morphTargetInfluences &&
                  obj.morphTargetDictionary &&
                  (currentProcessingPartName === "Decay" ||
                    currentProcessingPartName === "Filling")
                ) {
                  const morphTargetNames = Object.keys(
                    obj.morphTargetDictionary,
                  );
                  sortedTreatments.forEach((treatment) => {
                    if (!treatment.surfaces) return;
                    Object.entries(treatment.surfaces).forEach(
                      ([surfaceName, surfaceData]) => {
                        if (surfaceData.decaySeverity !== undefined) {
                          const morphTarget = findExactSurfaceMorphTarget(
                            morphTargetNames,
                            surfaceName,
                            currentProcessingPartName,
                          );
                          if (morphTarget) {
                            const targetIndex =
                              obj.morphTargetDictionary[morphTarget];
                            obj.morphTargetInfluences[targetIndex] =
                              surfaceData.decaySeverity;
                            if (currentProcessingPartName === "Filling")
                              obj.visible = true; // Ensure filling is visible if morph target applied
                          }
                        }
                      },
                    );
                  });
                }
                obj.material.side = THREE.DoubleSide;
                obj.castShadow = true;
                obj.receiveShadow = true;
                if (!obj.userData.originalMaterial)
                  obj.userData.originalMaterial = obj.material.clone();
              }
            });

            // Ensure all materials are transparent for single view (optional, can be controlled)
            // ensureAllMaterialsTransparent(child, 0.9); // Example: 90% opacity

            // Positioning and Scaling for single tooth view
            child.position.set(0, 0, 0); // Centered
            // Initial scale can be adjusted here if needed, e.g., child.scale.set(1,1,1);
            // The main scaling will be done on the toothContainerRef

            toothContainerRef.current.add(child);
            const animations = (animationCache.get(modelPath) || []).map(
              (anim) => createYPositionOnlyClip(anim),
            );
            if (animations.length) {
              processAnimations(
                toothNumber,
                animations,
                child,
                currentProcessingPartName,
              );
            }
          } catch (err) {
            console.error(
              `[SingleTooth] Error loading model part ${modelFileBaseNameForURL}:`,
              err,
            );
          }
        }
      }

      // Scale the entire tooth container for a good close-up view
      toothContainerRef.current.scale.set(2.5, 2.5, 2.5); // Adjust scale as needed
      toothContainerRef.current.position.set(0, -0.1, 0); // Adjust y-offset if needed
      toothContainerRef.current.rotation.set(0, Math.PI * 0.25, 0); // Slight rotation for better view

      setIsLoading(false);
      if (renderer && scene && camera) {
        forceSceneUpdate(renderer, scene, camera);
      }
    },
    [
      patientType,
      cleanupToothModels,
      getTreatmentVisibility,
      processAnimations,
      renderer,
      scene,
      camera,
    ],
  );

  useEffect(() => {
    if (toothNumberToDisplay && patientTeeth) {
      const toothKey = Object.keys(patientTeeth).find(
        (key) =>
          patientTeeth[key].position_number === toothNumberToDisplay ||
          patientTeeth[key].number === toothNumberToDisplay,
      );
      const data = toothKey ? patientTeeth[toothKey] : null;

      if (data) {
        setCurrentToothData(data);
        loadToothModel(data, toothNumberToDisplay);
      } else {
        setCurrentToothData(null); // Tooth not found
        cleanupToothModels();
        setIsLoading(false);
        console.warn(
          `[SingleTooth] Tooth data for number ${toothNumberToDisplay} not found.`,
        );
      }
    } else if (!toothNumberToDisplay) {
      setCurrentToothData(null);
      cleanupToothModels();
      setIsLoading(false);
    }
  }, [toothNumberToDisplay, patientTeeth, loadToothModel, cleanupToothModels]);

  // Update visibility on treatmentVisibility context change
  useEffect(() => {
    if (
      !toothContainerRef.current ||
      !currentToothData ||
      !currentToothData.treatments
    )
      return;

    toothContainerRef.current.traverse((obj) => {
      if (obj.isMesh && obj.userData && obj.userData.treatmentId) {
        const { treatmentId } = obj.userData; // Removed unused treatmentName
        const isVisible = getTreatmentVisibility(
          toothNumberToDisplay,
          treatmentId,
        );
        if (obj.visible !== isVisible) {
          obj.visible = isVisible;
        }
      }
    });
    if (renderer && scene && camera) {
      forceSceneUpdate(renderer, scene, camera);
    }
  }, [
    treatmentVisibility,
    currentToothData,
    toothNumberToDisplay,
    getTreatmentVisibility,
    renderer,
    scene,
    camera,
  ]);

  // Camera setup for this specific view
  useEffect(() => {
    camera.position.set(0, 0.1, 0.6); // Adjust for optimal view of a single tooth
    camera.lookAt(0, 0, 0);
    camera.updateProjectionMatrix();
    if (renderer && scene) {
      renderer.render(scene, camera);
    }
  }, [camera, renderer, scene]);

  if (isLoading) {
    return (
      <Html position={[0, 0, 0]} center>
        <div
          style={{
            color: "white",
            background: "rgba(0,0,0,0.5)",
            padding: "10px",
            borderRadius: "5px",
          }}
        >
          Loading tooth {toothNumberToDisplay}...
        </div>
      </Html>
    );
  }

  if (!currentToothData) {
    return (
      <Html position={[0, 0, 0]} center>
        <div
          style={{
            color: "white",
            background: "rgba(0,0,0,0.5)",
            padding: "10px",
            borderRadius: "5px",
          }}
        >
          Tooth {toothNumberToDisplay} data not available.
        </div>
      </Html>
    );
  }

  return (
    <>
      {/* The tooth models are added to toothContainerRef directly */}
      {currentToothData && currentToothData.treatments && (
        <Html
          fullscreen
          zIndexRange={[10, 0]}
          style={{
            pointerEvents: "none",
            position: "absolute",
            top: "0px",
            left: "0px",
            width: "300px",
          }} // Adjust style for positioning
        >
          <div
            style={{
              pointerEvents: "auto",
              background: "rgba(50,50,50,0.8)",
              padding: "10px",
              borderRadius: "5px",
            }}
          >
            <h4 style={{ color: "white", margin: "0 0 10px 0" }}>
              Tooth #{toothNumberToDisplay}
            </h4>
            <TreatmentsListWrapper
              toothData={currentToothData} // Pass the specific tooth's data
              onToggleVisibility={(treatmentId) =>
                toggleTreatmentVisibility(toothNumberToDisplay, treatmentId)
              }
              getVisibility={(treatmentId) =>
                getTreatmentVisibility(toothNumberToDisplay, treatmentId)
              }
              isSingleToothView={true}
            />
          </div>
        </Html>
      )}
    </>
  );
};

export default SingleTooth;
