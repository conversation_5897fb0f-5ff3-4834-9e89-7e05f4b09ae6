import { useRef, useEffect, useState, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useThree } from "@react-three/fiber";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import * as THREE from "three";
import {
  applyAntiZFightingProperties,
  parseTreatmentName,
  applyMaterialByName,
} from "../../utils/materialUtils";
import { ensureAllMaterialsTransparent } from "../../utils/transparencyUtils";
// Debug utils removed
import { useTeeth } from "../../context/TeethContext";
import { Html } from "@react-three/drei";
import TreatmentsListWrapper from "../UI/TreatmentsListWrapper";
import { BASE_URL, TREATMENTS_VERSION } from "../../config/api";

// Import utility functions from modelUtils.js
import {
  modelCache,
  animationCache,
  sanitizeAnimationTracks,
  createPositionLockedMixer,
  getModelParts,
  cloneModel,
  findExactSurfaceMorphTarget,
  forceSceneUpdate,
  clearAllTeethFromScene,
  // checkForStateSpecificModel, // Removed unused import
  // getModelPathForPatientType, // No longer used directly for path construction here
} from "../../utils/modelUtils";

// Helper function to find the correct treatment index
const findTreatmentIndex = (
  treatments,
  treatment,
  partName = null,
  // debugPrefix = "", // Removed unused parameter
  meshName = null,
) => {
  // Special case: If meshName is provided, use it to find the exact treatment
  if (meshName) {
    // Extract treatment name from mesh name (e.g., "tooth_1_Apictomy_single_treatment" -> "Apictomy")
    const meshParts = meshName.split("_");
    if (meshParts.length >= 3) {
      const meshTreatmentName = meshParts[2];

      // Find the treatment with the exact name from the mesh
      const exactNameIndex = treatments.findIndex((t) => {
        // Removed unused 'idx'
        if (t.name.toLowerCase() === meshTreatmentName.toLowerCase()) {
          return true;
        }
        return false;
      });

      if (exactNameIndex >= 0) {
        return exactNameIndex;
      }
    }
  }

  // First, try to find an exact match by ID and name
  for (let idx = 0; idx < treatments.length; idx++) {
    const t = treatments[idx];

    // Check for exact ID match
    if (
      (treatment.Id && t.Id === treatment.Id) ||
      (treatment.id && t.id === treatment.id)
    ) {
      // Also check if names match to ensure we have the right treatment
      const { baseName: tBaseName } = parseTreatmentName(t.name);
      const { baseName: treatmentBaseName } = parseTreatmentName(
        treatment.name,
      );
      if (tBaseName.toLowerCase() === treatmentBaseName.toLowerCase()) {
        return idx;
      }
    }
  }

  // Then try to match by name exactly
  for (let idx = 0; idx < treatments.length; idx++) {
    const t = treatments[idx];
    const { baseName: tBaseName } = parseTreatmentName(t.name);
    const { baseName: treatmentBaseName } = parseTreatmentName(treatment.name);
    if (tBaseName.toLowerCase() === treatmentBaseName.toLowerCase()) {
      return idx;
    }
  }

  // If partName is provided, try to match by part name
  if (partName) {
    for (let idx = 0; idx < treatments.length; idx++) {
      const t = treatments[idx];

      if (t.name.toLowerCase() === partName.toLowerCase()) {
        return idx;
      }
    }
  }

  // Log that no match was found

  // Return -1 to indicate no match was found
  return -1;
};

export const SingleTreatment = ({ treatmentData }) => {
  // Get teeth data from context
  const {
    patientTeeth,
    getPatientType,
    getTreatmentVisibility,
    toggleTreatmentVisibility,
    treatmentVisibility,
  } = useTeeth();

  // Get the patient type
  const patientType = getPatientType();

  // State and refs
  const [toothData, setToothData] = useState(null);
  const [isLoading, setIsLoading] = useState(true); // Used to track loading state
  const pointersRef = useRef(new Map());
  const loaderRef = useRef(new GLTFLoader());
  const mountedRef = useRef(true);
  const mixersRef = useRef(new Map());
  const actionsRef = useRef(new Map());
  const frameCountRef = useRef(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const toothRef = useRef(null);

  // Get access to the scene and renderer from React Three Fiber
  const { scene, gl: renderer, camera } = useThree();

  // Cleanup function for tooth models
  const cleanupTooth = useCallback(() => {
    // Cleanup mixers and actions
    mixersRef.current.forEach((mixer) => mixer.stopAllAction());
    mixersRef.current.clear();
    actionsRef.current.clear();

    // Clear the tooth ref
    toothRef.current = null;

    // Use the utility function to clear all teeth from the scene
    if (scene) {
      clearAllTeethFromScene(scene);
    }

    // Force a scene update
    if (renderer && scene && camera) {
      forceSceneUpdate(renderer, scene, camera);
    }
  }, [scene, renderer, camera]);

  useEffect(() => {
    mountedRef.current = true;
    // loaderRef.current.setResourcePath("./TreatmentsV4/"); // Removed, using full URLs from getModelUrl
    loaderRef.current.setCrossOrigin("anonymous");

    // Setup global animation controls for single treatment view
    if (typeof window !== "undefined") {
      window.singleTreatmentAnimationControls = {
        play: playAnimations,
        pause: pauseAnimations,
        reset: resetAnimations,
        isPlaying: () => isPlaying,
        isPaused: () => isPaused,
        currentFrame: () => frameCountRef.current,
      };
    }

    return () => {
      mountedRef.current = false;
      cleanupTooth();
      // Clean up global controls
      if (typeof window !== "undefined") {
        delete window.singleTreatmentAnimationControls;
      }
    };
  }, [
    cleanupTooth,
    playAnimations,
    pauseAnimations,
    resetAnimations,
    isPlaying,
    isPaused,
  ]);

  const processAnimations = useCallback((animations, scene, treatmentName) => {
    if (!animations || animations.length === 0) return;

    // Create a position-locked mixer for the tooth to prevent X/Z movement
    let mixer = createPositionLockedMixer(scene);
    mixersRef.current.set(1, mixer); // Use 1 as the tooth number for single treatment

    animations.forEach((clip) => {
      // Log position track details
      clip.tracks.forEach((track) => {
        if (track.name.endsWith(".position")) {
          // Empty
        }
      });

      const validTracks = clip.tracks.filter((track) => {
        const nodeName = track.name.split(".")[0];
        return scene.getObjectByName(nodeName);
      });

      if (validTracks.length === 0) return;

      const validClip = new THREE.AnimationClip(
        clip.name,
        clip.duration,
        validTracks,
      );

      // Create a unique key for this action
      const actionKey = `1_${treatmentName}_${validClip.name}`;

      // Create the action
      const action = mixer.clipAction(validClip);
      action.setLoop(THREE.LoopOnce);
      action.clampWhenFinished = true;

      actionsRef.current.set(actionKey, action);
    });
  }, []);

  const playAnimations = () => {
    if (!isPaused) {
      frameCountRef.current = 0;
    }

    actionsRef.current.forEach((action) => {
      action.stop();
      action.reset();
    });

    actionsRef.current.forEach((action) => {
      action.play();
      action.setEffectiveWeight(1);
      action.paused = false;
    });

    setIsPlaying(true);
    setIsPaused(false);
  };

  const pauseAnimations = () => {
    actionsRef.current.forEach((action) => {
      action.paused = true;
    });
    setIsPaused(true);
  };

  const resetAnimations = () => {
    frameCountRef.current = 0;

    actionsRef.current.forEach((action) => {
      action.stop();
      action.reset();
    });

    setIsPlaying(false);
    setIsPaused(false);
  };

  useFrame((_, delta) => {
    if (isPlaying && !isPaused) {
      // Log mesh positions before animation update
      if (frameCountRef.current === 0) {
        scene.traverse((obj) => {
          if (obj.isMesh && obj.name.includes("tooth")) {
            // Empty
          }
        });
      }

      mixersRef.current.forEach((mixer) => mixer.update(delta));

      const fps = 30;
      frameCountRef.current += delta * fps;

      // Log positions periodically during animation
      if (
        Math.floor(frameCountRef.current) % 10 === 0 &&
        frameCountRef.current < 31
      ) {
        scene.traverse((obj) => {
          if (obj.isMesh && obj.name.includes("tooth")) {
            // Empty
          }
        });
      }

      if (frameCountRef.current >= 30) {
        pauseAnimations();
      }
    }
  });

  const loadToothModel = useCallback(
    async (toothData, pointer) => {
      if (!mountedRef.current) {
        return;
      }

      if (!pointer || !pointer.isObject3D) {
        return;
      }

      const hasMissingToothTreatment =
        toothData.treatments &&
        toothData.treatments.some(
          (treatment) => treatment.missing_tooth_indicator,
        );

      if (toothData.marked_as_missing || hasMissingToothTreatment) {
        setIsLoading(false);
        return;
      }

      const positionMap = {
        UL8: 1,
        UL7: 2,
        UL6: 3,
        UL5: 4,
        UL4: 5,
        UL3: 6,
        UL2: 7,
        UL1: 8,
        UR1: 9,
        UR2: 10,
        UR3: 11,
        UR4: 12,
        UR5: 13,
        UR6: 14,
        UR7: 15,
        UR8: 16,
        LR8: 17,
        LR7: 18,
        LR6: 19,
        LR5: 20,
        LR4: 21,
        LR3: 22,
        LR2: 23,
        LR1: 24,
        LL1: 25,
        LL2: 26,
        LL3: 27,
        LL4: 28,
        LL5: 29,
        LL6: 30,
        LL7: 31,
        LL8: 32,
      };

      const number = positionMap[toothData.position] || 1;

      const existingParts = pointer.children.filter((c) =>
        c.name.startsWith(`tooth_${number}_`),
      );
      existingParts.forEach((part) => {
        pointer.remove(part);
        part.traverse((child) => {
          if (child.isMesh) {
            child.geometry?.dispose();
            if (Array.isArray(child.material)) {
              child.material.forEach((m) => m.dispose());
            } else {
              child.material?.dispose();
            }
          }
        });
      });

      // Sort treatments by creation date (newest first)
      const sortedTreatments = toothData.treatments
        ? [...toothData.treatments].sort((a, b) => {
            const dateA = new Date(a.created_at || 0);
            const dateB = new Date(b.created_at || 0);
            return dateB - dateA; // Newest first
          })
        : [];

      const modelParts = getModelParts(toothData);
      let primaryModel = null;

      const decaySurfaces =
        sortedTreatments.length > 0 && sortedTreatments[0].surfaces
          ? Object.keys(sortedTreatments[0].surfaces)
          : [];

      for (const basePartName of modelParts) {
        // Renamed 'part' to 'basePartName' for clarity
        let filesToLoadThisIteration = [];
        const toothIdInFilename =
          patientType === "CHILDREN" ? `${number}C` : `${number}`;

        const isInlay = basePartName.toLowerCase().includes("inlay");
        const isOnlay = basePartName.toLowerCase().includes("onlay");

        if (isInlay || isOnlay) {
          const type = isInlay ? "Inlay" : "Onlay";
          // Load the restoration model (e.g., Inlay_1_Base.glb)
          filesToLoadThisIteration.push({
            originalTreatmentName: type,
            modelFileIdentifier: `${type}_${toothIdInFilename}_Base`,
            modelFileIdentifierForObjectName: `${type}_Base`,
          });
          // Load the prepared tooth model (e.g., InlayTooth_1_Base.glb)
          filesToLoadThisIteration.push({
            originalTreatmentName: `${type}Tooth`,
            modelFileIdentifier: `${type}Tooth_${toothIdInFilename}_Base`,
            modelFileIdentifierForObjectName: `${type}Tooth_Base`,
          });
        } else {
          const modelFileId =
            basePartName === "Default"
              ? toothIdInFilename
              : `${toothIdInFilename}_${basePartName}`;
          filesToLoadThisIteration.push({
            originalTreatmentName: basePartName,
            modelFileIdentifier: modelFileId,
            modelFileIdentifierForObjectName: basePartName,
          });
        }

        for (const loadDetails of filesToLoadThisIteration) {
          const currentProcessingPartName = loadDetails.originalTreatmentName; // e.g., "Inlay", "Crown", "Default"
          const modelFileBaseNameForURL = loadDetails.modelFileIdentifier; // e.g., "1_Inlay_Base", "1_Crown", "1" (for default)
          const objectNameSuffix = loadDetails.modelFileIdentifierForObjectName; // e.g., "Inlay_Base", "Crown", "Default"

          // For single_treatment view, assume 'STATE_A' (skull-like view) for getModelUrl
          const treatmentDetails = sortedTreatments.find((t) => {
            const { baseName } = parseTreatmentName(t.name);
            return (
              baseName.toLowerCase() === currentProcessingPartName.toLowerCase()
            );
          });

          const rawModelName =
            treatmentDetails?.name ||
            treatmentDetails?.model_alias ||
            currentProcessingPartName;

          const { baseName: baseTreatmentName, material: treatmentMaterial } =
            parseTreatmentName(rawModelName);
          const modelFileName = modelFileBaseNameForURL.replace(
            currentProcessingPartName,
            baseTreatmentName,
          );
          const modelPath = `${BASE_URL}/${TREATMENTS_VERSION}/${baseTreatmentName}/${modelFileName}.glb`;

          if (!modelPath) {
            // console.warn(`Could not determine model path for ${modelFileBaseNameForURL}`);
            continue; // Skip if no path
          }

          try {
            let gltf;
            if (modelCache.has(modelPath)) {
              gltf = {
                scene: cloneModel(
                  modelCache.get(modelPath).scene,
                  "single_treatment",
                ),
              };
            } else {
              gltf = await new Promise((resolve, reject) => {
                loaderRef.current.load(modelPath, resolve, undefined, reject);
              });
            }

            if (!modelCache.has(modelPath)) {
              modelCache.set(modelPath, {
                scene: cloneModel(gltf.scene, "single_treatment"),
              });
              if (gltf.animations?.length) {
                animationCache.set(
                  modelPath,
                  gltf.animations.map((a) => a.clone()),
                );
              }
            }

            const cachedAnimations = animationCache.get(modelPath) || [];

            const animations = cachedAnimations.map((anim) => {
              // This function doesn't exist! Should be sanitizeAnimationTracks
              const sanitized = sanitizeAnimationTracks(anim);

              return sanitized;
            });

            if (!mountedRef.current) return;

            if (
              !gltf.scene ||
              !gltf.scene.children ||
              gltf.scene.children.length === 0
            ) {
              // console.warn(`GLTF scene is empty or invalid for ${modelPath}`);
              continue;
            }

            // Check if the GLTF has multiple root children
            if (gltf.scene.children.length > 1) {
              // Create a new group to contain all children
              const wrapperGroup = new THREE.Group();
              wrapperGroup.name = "wrapped_root";

              // Move all children to the wrapper group
              while (gltf.scene.children.length > 0) {
                wrapperGroup.add(gltf.scene.children[0]);
              }

              // Add the wrapper group back to the scene
              gltf.scene.add(wrapperGroup);
            }

            const child = gltf.scene.children[0];

            // Apply material AFTER child is defined
            if (treatmentMaterial) {
              applyMaterialByName(child, treatmentMaterial);

              // Check material after application
              child.traverse((obj) => {
                if (obj.isMesh) {
                  // Empty
                }
              });
            }

            child.position.set(0, 0, 0);
            child.rotation.set(0, 0, 0);
            child.name = `tooth_${number}_${objectNameSuffix}_single_treatment`; // Use objectNameSuffix

            child.userData = child.userData || {};
            child.userData.createdAt = Date.now();
            child.userData.viewType = "single_treatment";

            // Collect all meshes
            const allMeshes = [];
            child.traverse((obj) => {
              if (obj.isMesh) {
                allMeshes.push(obj);
              }
            });

            // Setup meshes and apply morph targets
            child.traverse((part) => {
              if (part.isMesh) {
                part.userData = part.userData || {};
                // Don't store originalMaterial yet - we'll do it after all processing
                part.userData.number = number;
                part.userData.type = "tooth";
                part.userData.isInteractive = true;
                part.userData.viewType = "single_treatment";

                // Only apply default properties to filling objects, coloring will be handled later
                if (currentProcessingPartName === "Filling") {
                  // Use currentProcessingPartName
                  // Clone the original material
                  const fillingMaterial = part.material.clone();

                  // Set transparency properties
                  fillingMaterial.transparent = true;
                  fillingMaterial.opacity = 0.8; // 80% opacity as requested

                  // Apply anti-z-fighting properties using our utility function
                  // Don't set color here - it will be set later based on surface data
                  part.material = applyAntiZFightingProperties(
                    fillingMaterial,
                    0, // Index 0 for default treatment
                    true, // Make it transparent
                  );

                  part.visible = true;
                }

                // Apply transparency for all treatments
                if (toothData.treatments && toothData.treatments.length > 0) {
                  // Find the treatment that corresponds to this part
                  const treatmentForPart = sortedTreatments.find((t) => {
                    const { baseName } = parseTreatmentName(t.name);
                    return baseName === currentProcessingPartName;
                  });

                  // If we have multiple treatments and this part corresponds to one of them
                  if (sortedTreatments.length > 1 && treatmentForPart) {
                    // Find the index of this treatment in the sorted array
                    const index = sortedTreatments.indexOf(treatmentForPart);

                    // Store the treatment information in the object's userData
                    part.userData.treatmentIndex = index;
                    part.userData.treatmentName = treatmentForPart.name;
                    part.userData.treatmentId =
                      treatmentForPart.Id || treatmentForPart.id;

                    // Apply anti-z-fighting properties to the material
                    part.material = applyAntiZFightingProperties(
                      part.material,
                      index,
                      true, // Make it transparent
                    );

                    // We're not modifying positions anymore, only transparency
                    // This keeps the treatments in their original positions

                    // Set render order based on index (higher index = rendered first)
                    part.renderOrder = -index; // Negative so newer treatments render later

                    // Check if this treatment should be visible
                    // Use a consistent ID for the treatment
                    // Create a unique ID by combining treatment ID and index
                    const baseId =
                      treatmentForPart.Id ||
                      treatmentForPart.id ||
                      `treatment_${index}`;

                    // Find the index of this treatment in the toothData.treatments array
                    // Use the part name and mesh name to match with the treatment name
                    const treatmentIndex = findTreatmentIndex(
                      toothData.treatments,
                      treatmentForPart,
                      currentProcessingPartName, // Use currentProcessingPartName
                      "Main treatment -",
                      part.name, // Pass the mesh name to help with matching
                    );

                    // Special case for specific treatments
                    let actualIndex =
                      treatmentIndex >= 0 ? treatmentIndex : index;

                    // Check if this is a specific treatment that needs special handling
                    if (part.name.includes("Externalsinuslift")) {
                      // Find the Externalsinuslift treatment in the toothData.treatments array
                      const externalIndex = toothData.treatments.findIndex(
                        (t) => t.name.toLowerCase() === "externalsinuslift",
                      );
                      if (externalIndex >= 0) {
                        actualIndex = externalIndex;
                      }
                    } else if (part.name.includes("RetaineRoot")) {
                      // Find the RetaineRoot treatment in the toothData.treatments array
                      const retainedIndex = toothData.treatments.findIndex(
                        (t) => t.name.toLowerCase() === "retaineroot",
                      );
                      if (retainedIndex >= 0) {
                        actualIndex = retainedIndex;
                      }
                    }

                    const treatmentId = `${baseId}_${actualIndex}`;

                    part.userData.treatmentId = treatmentId; // Store for future reference
                    part.userData.baseId = baseId; // Store the original ID for reference
                    part.userData.treatmentIndex = actualIndex; // Store the index for reference
                    part.userData.treatmentName = treatmentForPart.name; // Store the name for reference

                    const isVisible = getTreatmentVisibility(
                      toothData.position_number || toothData.number,
                      treatmentId,
                    );
                    part.visible = isVisible;
                  }
                }

                // Apply morph targets for decay and filling parts
                if (
                  part.morphTargetInfluences &&
                  part.morphTargetDictionary &&
                  toothData.treatments &&
                  toothData.treatments.length > 0 &&
                  (currentProcessingPartName === "Decay" ||
                    currentProcessingPartName === "Filling" ||
                    currentProcessingPartName === "Onlay")
                ) {
                  const morphTargetNames = Object.keys(
                    part.morphTargetDictionary,
                  );

                  // Process each treatment
                  sortedTreatments.forEach((treatment) => {
                    // Skip treatments without surfaces
                    if (!treatment.surfaces) return;

                    // Process each surface in the treatment
                    Object.entries(treatment.surfaces).forEach(
                      ([surfaceName, surfaceData]) => {
                        const severity =
                          surfaceData.decaySeverity !== undefined
                            ? surfaceData.decaySeverity
                            : surfaceData.level;
                        if (severity !== undefined) {
                          // Find the exact morph target for this surface
                          const morphTarget = findExactSurfaceMorphTarget(
                            morphTargetNames,
                            surfaceName,
                            currentProcessingPartName, // Use currentProcessingPartName
                          );

                          if (morphTarget) {
                            const targetIndex =
                              part.morphTargetDictionary[morphTarget];
                            part.morphTargetInfluences[targetIndex] = severity;

                            // For filling parts, check visibility and apply anti-z-fighting
                            if (currentProcessingPartName === "Filling") {
                              // Use currentProcessingPartName
                              // Check if this treatment should be visible
                              // Use a consistent ID for the treatment
                              // Create a unique ID by combining treatment ID and index
                              const sortedIndex =
                                sortedTreatments.indexOf(treatment);
                              const baseId =
                                treatment.Id ||
                                treatment.id ||
                                `treatment_${sortedIndex}`;

                              // Find the index of this treatment in the toothData.treatments array
                              // Use the part name and mesh name to match with the treatment name
                              const treatmentIndex = findTreatmentIndex(
                                toothData.treatments,
                                treatment,
                                currentProcessingPartName, // Use currentProcessingPartName
                                "Morph target -",
                                part.name, // Pass the mesh name to help with matching
                              );

                              // Use the same index as in the UI
                              const actualIndex =
                                treatmentIndex >= 0
                                  ? treatmentIndex
                                  : sortedIndex;
                              const treatmentId = `${baseId}_${actualIndex}`;

                              part.userData.treatmentId = treatmentId; // Store for future reference
                              part.userData.baseId = baseId; // Store the original ID for reference
                              part.userData.treatmentIndex = actualIndex; // Store the index for reference
                              part.userData.treatmentName = treatment.name; // Store the name for reference

                              const isVisible = getTreatmentVisibility(
                                toothData.position_number || toothData.number,
                                treatmentId,
                              );
                              part.visible = isVisible;

                              // We already have the index from above

                              // Always apply anti-z-fighting properties for consistent transparency
                              part.material = applyAntiZFightingProperties(
                                part.material,
                                actualIndex,
                                true, // Make it transparent
                              );

                              // We're not modifying positions anymore, only transparency
                              // This keeps the treatments in their original positions

                              // Set render order
                              part.renderOrder = -actualIndex;
                            }
                          }
                        }
                      },
                    );
                  });
                }

                part.material.side = THREE.DoubleSide;
                part.castShadow = true;
                part.receiveShadow = true;
              }
            });

            // Apply material coloring for Decay
            if (
              currentProcessingPartName === "Decay" && // Use currentProcessingPartName
              toothData.treatments &&
              toothData.treatments.length > 0
            ) {
              // Process each treatment that has surfaces
              sortedTreatments.forEach((treatment, treatmentIndex) => {
                if (!treatment.surfaces) return;

                Object.entries(treatment.surfaces).forEach(
                  ([surfaceName, surfaceData]) => {
                    if (surfaceData.decaySeverity > 0) {
                      // The specific material name we want to match exactly
                      const targetMaterialName = `decay_${surfaceName.toLowerCase()}`;

                      // Look through all meshes for exact match
                      allMeshes.forEach((mesh) => {
                        const materialName = (
                          mesh.material?.name || ""
                        ).toLowerCase();

                        // Only color if the material name is EXACTLY "decay_[surfaceName]"
                        if (materialName === targetMaterialName) {
                          // Clone the material to avoid affecting other meshes
                          if (!mesh.userData.originalMaterial) {
                            mesh.userData.originalMaterial =
                              mesh.material.clone();
                          }

                          // Create a new material with anti-z-fighting properties
                          const decayMaterial =
                            mesh.userData.originalMaterial.clone();
                          decayMaterial.color.set(0x000000);

                          // Apply anti-z-fighting properties with transparency
                          mesh.material = applyAntiZFightingProperties(
                            decayMaterial,
                            treatmentIndex,
                            true, // Make it transparent
                          );

                          // We're not modifying positions anymore, only transparency
                          // This keeps the treatments in their original positions

                          // Check if this treatment should be visible
                          // Use a consistent ID for the treatment
                          // Create a unique ID by combining treatment ID and index
                          const baseId =
                            treatment.Id ||
                            treatment.id ||
                            `treatment_${treatmentIndex}`;

                          // Find the index of this treatment in the toothData.treatments array
                          // Use the treatment name and mesh name to match with the treatment name
                          const actualTreatmentIndex = findTreatmentIndex(
                            toothData.treatments,
                            treatment,
                            currentProcessingPartName, // Use currentProcessingPartName
                            "Decay material -",
                            mesh.name, // Pass the mesh name to help with matching
                          );

                          // Use the same index as in the UI
                          const actualIndex =
                            actualTreatmentIndex >= 0
                              ? actualTreatmentIndex
                              : treatmentIndex;
                          const treatmentId = `${baseId}_${actualIndex}`;

                          mesh.userData.treatmentId = treatmentId; // Store for future reference
                          mesh.userData.baseId = baseId; // Store the original ID for reference
                          mesh.userData.treatmentIndex = actualIndex; // Store the index for reference
                          mesh.userData.treatmentName = treatment.name; // Store the name for reference

                          const isVisible = getTreatmentVisibility(
                            toothData.position_number || toothData.number,
                            treatmentId,
                          );
                          mesh.visible = isVisible;
                        }
                      });
                    }
                  },
                );
              });
            }

            // Apply material coloring for Filling
            if (
              currentProcessingPartName === "Filling" &&
              decaySurfaces.length > 0
            ) {
              // Use currentProcessingPartName
              decaySurfaces.forEach((surfaceName) => {
                const surfaceData =
                  toothData.treatments[0].surfaces[surfaceName];

                if (surfaceData && surfaceData.decaySeverity > 0) {
                  // Look through all meshes for pattern match
                  allMeshes.forEach((mesh) => {
                    const materialName = mesh.material?.name || "";
                    // Log filling model materials
                    // if (materialName.toLowerCase().includes("filling")) { // Removed empty block
                    // }

                    // Check if material name contains the surface name
                    // Pattern: Filling_[number]_[SurfaceName]_Base
                    if (
                      materialName.toLowerCase().includes("filling") &&
                      materialName.includes(`_${surfaceName}_`)
                    ) {
                      // Clone the material to avoid affecting other meshes
                      if (!mesh.userData.originalMaterial) {
                        mesh.userData.originalMaterial = mesh.material.clone();
                      }

                      // Create a new material with anti-z-fighting properties
                      const fillingMaterial =
                        mesh.userData.originalMaterial.clone();
                      fillingMaterial.color.set(0xffff00);
                      fillingMaterial.emissive.set(0x333300);

                      // Apply anti-z-fighting properties with transparency
                      mesh.material = applyAntiZFightingProperties(
                        fillingMaterial,
                        0, // Index 0 for the newest treatment
                        true, // Make it transparent
                      );

                      // Check if this treatment should be visible
                      // Use a consistent ID for the treatment
                      // Create a unique ID by combining treatment ID and index
                      const treatment = toothData.treatments[0];
                      const baseId =
                        treatment.Id || treatment.id || "treatment_0";

                      // Find the index of this treatment in the toothData.treatments array
                      const treatmentIndex = 0; // It's the first treatment
                      const treatmentId = `${baseId}_${treatmentIndex}`;

                      mesh.userData.treatmentId = treatmentId; // Store for future reference
                      mesh.userData.baseId = baseId; // Store the original ID for reference
                      mesh.userData.treatmentIndex = treatmentIndex; // Store the index for reference
                      mesh.userData.treatmentName = treatment.name; // Store the name for reference

                      const isVisible = getTreatmentVisibility(
                        toothData.position_number || toothData.number,
                        treatmentId,
                      );
                      mesh.visible = isVisible;
                    }
                  });
                }
              });
            }

            // Get the first treatment name for primary model logic
            const firstTreatmentNameForTooth =
              toothData.treatments && toothData.treatments.length > 0
                ? toothData.treatments[0].name
                : "Default";

            // Set the primary model based on the original treatment name associated with this loaded part
            if (currentProcessingPartName === firstTreatmentNameForTooth) {
              primaryModel = child;
            }

            // Ensure all materials in the model are transparent, unless a specific material was already applied
            if (!treatmentMaterial) {
              ensureAllMaterialsTransparent(child, 0.8);
            }

            if (number >= 9 && number <= 24) {
              // This logic might need review for single view if tooth can be lower
              child.scale.set(-child.scale.x, child.scale.y, -child.scale.z);
            }

            if (
              firstTreatmentNameForTooth === "Decay" ||
              firstTreatmentNameForTooth === "Filling"
            ) {
              child.rotation.set(-89.5, 0, 0);
            }

            // For single treatment view, position the tooth exactly in the center
            // Perfect center position with no elevation
            child.position.set(0, 0, 0);

            // Scale the model to make it much more visible but maintain proportions
            const currentScale = child.scale.x;
            // Increase scale by 2.5x for an extreme close-up view
            child.scale.set(
              currentScale * 2.5,
              currentScale * 2.5,
              currentScale * 2.5,
            );

            // Log the final position, rotation and scale

            // Store originalMaterial AFTER all material processing is complete
            child.traverse((obj) => {
              if (obj.isMesh && !obj.userData.originalMaterial) {
                obj.userData.originalMaterial = obj.material.clone();
              }
            });

            // Add the model to the pointer
            pointer.add(child);

            // If the pointer is not in the scene, add it back
            if (!pointer.parent && scene) {
              scene.add(pointer);

              pointer.userData = pointer.userData || {};
              pointer.userData.scene = scene;
            }

            // Check scene hierarchy
            // Check bounds
            const box = new THREE.Box3().setFromObject(child);
            box.getSize(new THREE.Vector3());
            box.getCenter(new THREE.Vector3());

            if (renderer && scene && camera) {
              forceSceneUpdate(renderer, scene, camera);
            }

            if (animations.length) {
              processAnimations(animations, pointer, currentProcessingPartName); // Pass currentProcessingPartName
            }
          } catch (err) {
            console.error(
              `Error loading tooth model for part: ${modelFileBaseNameForURL}`,
              err,
            );
          }
        } // End of inner loop (filesToLoadThisIteration)
      } // End of outer loop (modelParts)

      // Store the primary model in the ref
      if (primaryModel) {
        toothRef.current = primaryModel;
      }

      setIsLoading(false);
    },
    [
      processAnimations,
      renderer,
      scene,
      camera,
      getTreatmentVisibility,
      patientType,
    ],
  );

  // Initialize tooth data from patientTeeth
  useEffect(() => {
    if (treatmentData) {
      const toothToDisplay = {
        position: "UL8", // Default position
        status: "status",
        lastTreatment: "01-01-2025",
        notes: "Single tooth for treatment view",
        marked_as_missing: false,
        treatments: [
          {
            Id: treatmentData.Id,
            ctid: "222",
            name: treatmentData.Name,
            full_tooth_treatment: true,
            created_at: "01-01-2025",
          },
        ],
      };
      setToothData(toothToDisplay);
    } else if (patientTeeth && Object.keys(patientTeeth).length > 0) {
      const firstToothKey = Object.keys(patientTeeth)[0];
      const firstTooth = patientTeeth[firstToothKey];

      if (firstTooth) {
        setToothData(firstTooth);
      }
    } else {
      const defaultTooth = {
        position: "UL8",
        status: "status",
        lastTreatment: "01-01-2025",
        notes: "Default tooth for single treatment view",
        marked_as_missing: false,
        treatments: [
          {
            id: "111",
            ctid: "222",
            name: "BoneGraft",
            full_tooth_treatment: true,
            created_at: "01-01-2025",
          },
        ],
      };

      setToothData(defaultTooth);
    }
  }, [patientTeeth, treatmentData]); // Only depend on patientTeeth, not toothData

  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data && event.data.type === "single_treatment_data") {
        setToothData(event.data.tooth);
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  useEffect(() => {
    if (!scene) {
      return;
    }

    const pointer = new THREE.Group();
    pointer.name = `tooth_pointer_1`;
    pointer.position.set(0, 0, 0);
    pointersRef.current.set(1, pointer);

    scene.add(pointer);

    // Store a reference to the scene to prevent garbage collection
    pointer.userData = pointer.userData || {};
    pointer.userData.scene = scene;
    pointer.userData.isMainPointer = true;

    const currentPointersRef = pointersRef.current;

    return () => {
      if (pointer.parent) {
        pointer.parent.remove(pointer);
      }
      currentPointersRef.clear();
    };
  }, [scene, camera, renderer]);

  useEffect(() => {
    if (toothData && pointersRef.current.size > 0) {
      const pointer = pointersRef.current.get(1);

      if (pointer) {
        if (!pointer.parent && scene) {
          scene.add(pointer);
        }

        cleanupTooth();
        const normalizedToothData = {
          ...toothData,
          treatments: toothData.treatments
            ? toothData.treatments.map((t) => ({
                ...t,
                name: t.name === "Inlay" ? "Onlay" : t.name,
              }))
            : [],
        };
        loadToothModel(normalizedToothData, pointer);
      }
    }
  }, [toothData, cleanupTooth, loadToothModel, scene]);

  // Update model visibility when treatmentVisibility changes
  useEffect(() => {
    if (!toothRef.current || !toothData || !toothData.treatments) return;

    // Log that we're updating visibility

    // Update visibility for all treatments in the model
    toothRef.current.traverse((obj) => {
      if (obj.isMesh && obj.userData && obj.userData.treatmentId) {
        const treatmentId = obj.userData.treatmentId;
        const toothNumber = toothData.position_number || toothData.number;
        const newVisibility = getTreatmentVisibility(toothNumber, treatmentId); // Renamed to avoid conflict

        // Only log if visibility is changing
        if (obj.visible !== newVisibility) {
          obj.visible = newVisibility;
        }
      }
    });
    // Log the current visibility state for debugging
    // The forEach loop below was for debugging and is no longer needed.
    /*
    toothData.treatments.forEach((treatment, index) => {
      const baseId = treatment.Id || treatment.id || `treatment_${index}`;
      const treatmentId = `${baseId}_${index}`;
      const toothNumber = toothData.position_number || toothData.number;
      // const isVisible = getTreatmentVisibility(toothNumber, treatmentId); // Unused variable
    });
    */

    // Force a render to update the scene
    if (renderer && scene && camera) {
      forceSceneUpdate(renderer, scene, camera);
    }
  }, [
    treatmentVisibility,
    toothData,
    getTreatmentVisibility,
    renderer,
    scene,
    camera,
  ]);

  // Ensure camera is set up for rotation
  useEffect(() => {
    if (!camera || !renderer) return;

    // Set camera position for a good view of the tooth
    camera.position.set(0, 0, 0.4);
    camera.lookAt(0, 0, 0);
    camera.updateProjectionMatrix();

    // Force a render to update the scene
    if (renderer && scene) {
      // renderer.render(scene, camera); // This was causing an issue, visibility is handled by getTreatmentVisibility
    }
  }, [camera, renderer, scene]); // Removed getTreatmentVisibility from deps as it's stable

  return (
    <group>
      {isLoading && (
        <Html position={[0, 0, 0]} center>
          <div
            style={{
              background: "rgba(255, 255, 255, 0.8)",
              padding: "10px 20px",
              borderRadius: "8px",
              boxShadow: "0 2px 10px rgba(0, 0, 0, 0.1)",
              fontFamily: "Arial, sans-serif",
            }}
          >
            Loading tooth model...
          </div>
        </Html>
      )}
      {toothData && (
        <Html
          fullscreen
          zIndexRange={[10, 0]}
          style={{ pointerEvents: "none" }}
        >
          <div style={{ pointerEvents: "auto" }}>
            <TreatmentsListWrapper
              toothData={toothData}
              onToggleVisibility={toggleTreatmentVisibility}
              getVisibility={getTreatmentVisibility}
            />
          </div>
        </Html>
      )}
    </group>
  );
};

export default SingleTreatment;
