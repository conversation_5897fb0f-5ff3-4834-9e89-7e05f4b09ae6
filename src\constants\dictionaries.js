export const TREATMENT_MAP = {
  Abcess: {
    key: "Abcess",
    output_treatment: "Abcess",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: "root",
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  Apictomy: {
    key: "Apicectomy",
    output_treatment: "Apictomy",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Bone Grafting": {
    key: "Bone Grafting",
    output_treatment: "BoneGraftingBlock",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Block Grafting": {
    key: "Block Grafting",
    output_treatment: "BoneGraftingBlock",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Gold Bridge Pontic": {
    key: "Gold Bridge Pontic",
    output_treatment: "Bridge_Gold",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: true,
  },
  "Porcelain Bonded Bridge": {
    key: "Porcelain Bonded Bridge",
    output_treatment: "PorcelainBondedBridge",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: true,
  },
  "Full Chrome Denture Fit": {
    key: "Full Chrome Denture Fit",
    output_treatment: "ChromePartialDenture",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Partial Chrome Denture": {
    key: "Partial Chrome Denture",
    output_treatment: "ChromePartialDenture",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  Clasp: {
    key: "Clasp",
    output_treatment: "Clasp",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: "crown",
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Closed Gap": {
    key: "Closed Gap",
    output_treatment: "ClosedGap",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Core BuildUp": {
    key: "Core BuildUp",
    output_treatment: "CoreBuildUp",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Tooth Prep For Crown": {
    key: "Tooth Prep For Crown",
    output_treatment: "ModifiedToothForCrown",
    crown: false,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Cerec Crown": {
    key: "Cerec Crown",
    output_treatment: "CrownForModedTooth_Cerec",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Composite Crown": {
    key: "Composite Crown",
    output_treatment: "CrownForModedTooth_Composite",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Emax Crown": {
    key: "Emax Crown",
    output_treatment: "CrownForModedTooth_Emax",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Empress Crown": {
    key: "Empress Crown",
    output_treatment: "CrownForModedTooth_Empress",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Gold Crown": {
    key: "Gold Crown",
    output_treatment: "CrownForModedTooth_Gold",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Metal Crown": {
    key: "Metal Crown",
    output_treatment: "CrownForModedTooth_Metal",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Porcelain Crown": {
    key: "Porcelain Crown",
    output_treatment: "CrownForModedTooth_Porcelain",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Silver Crown": {
    key: "Silver Crown",
    output_treatment: "CrownForModedTooth_Silver",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Zirconia Crown": {
    key: "Zirconia Crown",
    output_treatment: "CrownForModedTooth_Zirconia",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Arrested Caries": {
    key: "Arrested Caries",
    output_treatment: "Decay_ArrestedCaries",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  Cavity: {
    key: "Cavity",
    output_treatment: "Decay_Cavity",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  Caries: {
    key: "Caries",
    output_treatment: "Decay_Caries",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Early Caries": {
    key: "Early Caries",
    output_treatment: "Decay_EarlyCaries",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  Decay: {
    key: "Decay",
    output_treatment: "Decay",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Tooth Wear": {
    key: "Tooth Wear",
    output_treatment: "Decay_ToothWear",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Drifted Left": {
    key: "Drifted Left",
    output_treatment: "DriftedLeft",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Drifted Right": {
    key: "Drifted Right",
    output_treatment: "DriftedRight",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  Extraction: {
    key: "Extraction",
    output_treatment: "Extraction",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: true,
    remove_treatment_when_completed: true,
    bridge_treatment: false,
  },
  "Amalgam Filling": {
    key: "Amalgam Filling",
    output_treatment: "Filling_Amalgam",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Gold Filling": {
    key: "Gold Filling",
    output_treatment: "Filling_Gold",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Composite Filling": {
    key: "Composite Filling",
    output_treatment: "Filling_Composite",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Glass Ionomer Filling": {
    key: "Glass Ionomer Filling",
    output_treatment: "Filling_GlassIonomer",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Fissure Sealant": {
    key: "Fissure Sealant",
    output_treatment: "FissureSealant",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: "crown",
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Flexi Denture": {
    key: "Flexi Denture",
    output_treatment: "FlexiDenture",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Partial Acrylic Denture": {
    key: "Partial Acrylic Denture",
    output_treatment: "FlexiDenture",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Full Acrylic Denture": {
    key: "Full Acrylic Denture",
    output_treatment: "FlexiDenture",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Fractured Tooth - Large": {
    key: "Fractured Tooth - Large",
    output_treatment: "FracturedToothLarge",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Fractured Tooth - Small": {
    key: "Fractured Tooth - Small",
    output_treatment: "FracturedToothSmall",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Impacted Tooth": {
    key: "Impacted Tooth",
    output_treatment: "ImpactedToothCut",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Implant Bridge": {
    key: "Implant Bridge",
    output_treatment: "ImplantBridge",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: true,
  },
  "Implant Crown": {
    key: "Implant Crown",
    output_treatment: "CrownForImplant",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  Implant: {
    key: "Implant",
    output_treatment: "ImplantForCrown",
    crown: false,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  Inlay: {
    key: "Inlay",
    output_treatment: "Inlay",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Maryland Bridge": {
    key: "Maryland Bridge",
    output_treatment: "MarylandBridge",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: true,
  },
  "Maryland Wing": {
    key: "Maryland Wing",
    output_treatment: "MarylandWing",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  Onlay: {
    key: "Onlay",
    output_treatment: "Onlay",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: false,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Partially Erupted Tooth": {
    key: "Partially Erupted Tooth",
    output_treatment: "PartiallyEruptedTooth",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Pin Retention": {
    key: "Pin Retention",
    output_treatment: "PinRetention",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: "any",
    default_tooth_transparent: true,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Post & Core": {
    key: "Post & Core",
    output_treatment: "Post&Core",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: "any",
    default_tooth_transparent: true,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Retained Root": {
    key: "Retained Root",
    output_treatment: "RetainedRoot",
    crown: false,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Root Canal": {
    key: "Root Canal",
    output_treatment: "RootCanalTreatment",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: "any",
    default_tooth_transparent: true,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Sinus Drop": {
    key: "Sinus Drop",
    output_treatment: "Sinus_Drop",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Sinus Lift": {
    key: "Sinus Lift",
    output_treatment: "Sinus_Lift",
    crown: false,
    root: false,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Unerupted Tooth": {
    key: "Unerupted Tooth",
    output_treatment: "UneruptedTooth",
    crown: true,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Emax Veneer": {
    key: "Emax Veneer",
    output_treatment: "Veneers_Emax",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: "crown",
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Empress Veneer": {
    key: "Empress Veneer",
    output_treatment: "Veneers_Empress",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: "crown",
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Composite Veneer": {
    key: "Composite Veneer",
    output_treatment: "Veneers_Composite",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: "crown",
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Gold Veneer": {
    key: "Gold Veneer",
    output_treatment: "Veneers_Gold",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: "crown",
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Porcelain Veneer": {
    key: "Porcelain Veneer",
    output_treatment: "Veneers_Porcelain",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: "crown",
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Silver Veneer": {
    key: "Silver Veneer",
    output_treatment: "Veneers_Silver",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: "crown",
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Zirconia Veneer": {
    key: "Zirconia Veneer",
    output_treatment: "Veneers_Zirconia",
    crown: true,
    root: false,
    healthy_tooth_needed_when_missing: "crown",
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
  "Zygomatic Implant": {
    key: "Zygomatic Implant",
    output_treatment: "ZygomaticImplant",
    crown: false,
    root: true,
    healthy_tooth_needed_when_missing: null,
    default_tooth_transparent: false,
    full_tooth_treatment: true,
    remove_tooth_when_completed: false,
    remove_treatment_when_completed: false,
    bridge_treatment: false,
  },
};

export const TOOTH_SURFACES = {
  UL8: [
    "distal_buccal",
    "mesial_buccal",
    "mesial",
    "mesial_palatal",
    "distal_palatal",
    "distal",
    "distal_occlusal",
    "mesial_occlusal",
  ],
  UL7: [
    "distal_buccal",
    "mesial_buccal",
    "mesial",
    "mesial_palatal",
    "distal_palatal",
    "distal",
    "distal_occlusal",
    "mesial_occlusal",
  ],
  UL6: [
    "distal_buccal",
    "mesial_buccal",
    "mesial",
    "mesial_palatal",
    "distal_palatal",
    "distal",
    "distal_occlusal",
    "mesial_occlusal",
  ],
  UL5: ["buccal", "mesial", "palatal", "distal", "occlusal"],
  UL4: ["buccal", "mesial", "palatal", "distal", "occlusal"],
  UL3: ["buccal", "mesial", "palatal", "distal", "incisal"],
  UL2: ["buccal", "mesial", "palatal", "distal", "incisal"],
  UL1: ["buccal", "mesial", "palatal", "distal", "incisal"],

  UR1: ["buccal", "distal", "palatal", "mesial", "incisal"],
  UR2: ["buccal", "distal", "palatal", "mesial", "incisal"],
  UR3: ["buccal", "distal", "palatal", "mesial", "incisal"],
  UR4: ["buccal", "distal", "palatal", "mesial", "occlusal"],
  UR5: ["buccal", "distal", "palatal", "mesial", "occlusal"],
  UR6: [
    "mesial_buccal",
    "distal_buccal",
    "distal",
    "distal_palatal",
    "mesial_palatal",
    "mesial",
    "mesial_occlusal",
    "distal_occlusal",
  ],
  UR7: [
    "mesial_buccal",
    "distal_buccal",
    "distal",
    "distal_palatal",
    "mesial_palatal",
    "mesial",
    "mesial_occlusal",
    "distal_occlusal",
  ],
  UR8: [
    "mesial_buccal",
    "distal_buccal",
    "distal",
    "distal_palatal",
    "mesial_palatal",
    "mesial",
    "mesial_occlusal",
    "distal_occlusal",
  ],

  URE: ["buccal", "mesial", "palatal", "distal", "occlusal"],
  URD: ["buccal", "mesial", "palatal", "distal", "occlusal"],
  URC: ["buccal", "mesial", "palatal", "distal", "incisal"],
  URB: ["buccal", "mesial", "palatal", "distal", "incisal"],
  URA: ["buccal", "mesial", "palatal", "distal", "incisal"],
  ULA: ["buccal", "distal", "palatal", "mesial", "incisal"],
  ULB: ["buccal", "distal", "palatal", "mesial", "incisal"],
  ULC: ["buccal", "distal", "palatal", "mesial", "incisal"],
  ULD: ["buccal", "distal", "palatal", "mesial", "occlusal"],
  ULE: ["buccal", "distal", "palatal", "mesial", "occlusal"],

  LRE: ["lingual", "mesial", "buccal", "distal", "occlusal"],
  LRD: ["lingual", "mesial", "buccal", "distal", "occlusal"],
  LRC: ["lingual", "mesial", "buccal", "distal", "incisal"],
  LRB: ["lingual", "mesial", "buccal", "distal", "incisal"],
  LRA: ["lingual", "mesial", "buccal", "distal", "incisal"],
  LLA: ["lingual", "distal", "buccal", "mesial", "incisal"],
  LLB: ["lingual", "distal", "buccal", "mesial", "incisal"],
  LLC: ["lingual", "distal", "buccal", "mesial", "incisal"],
  LLD: ["lingual", "distal", "buccal", "mesial", "occlusal"],
  LLE: ["lingual", "distal", "buccal", "mesial", "occlusal"],

  LL8: [
    "distal_lingual",
    "mesial_lingual",
    "mesial",
    "mesial_buccal",
    "distal_buccal",
    "distal",
    "distal_occlusal",
    "mesial_occlusal",
  ],
  LL7: [
    "distal_lingual",
    "mesial_lingual",
    "mesial",
    "mesial_buccal",
    "distal_buccal",
    "distal",
    "distal_occlusal",
    "mesial_occlusal",
  ],
  LL6: [
    "distal_lingual",
    "mesial_lingual",
    "mesial",
    "mesial_buccal",
    "distal_buccal",
    "distal",
    "distal_occlusal",
    "mesial_occlusal",
  ],
  LL5: ["lingual", "mesial", "buccal", "distal", "occlusal"],
  LL4: ["lingual", "mesial", "buccal", "distal", "occlusal"],
  LL3: ["lingual", "mesial", "buccal", "distal", "incisal"],
  LL2: ["lingual", "mesial", "buccal", "distal", "incisal"],
  LL1: ["lingual", "mesial", "buccal", "distal", "incisal"],

  LR1: ["lingual", "distal", "buccal", "mesial", "incisal"],
  LR2: ["lingual", "distal", "buccal", "mesial", "incisal"],
  LR3: ["lingual", "distal", "buccal", "mesial", "incisal"],
  LR4: ["lingual", "distal", "buccal", "mesial", "occlusal"],
  LR5: ["lingual", "distal", "buccal", "mesial", "occlusal"],
  LR6: [
    "mesial_lingual",
    "distal_lingual",
    "distal",
    "distal_buccal",
    "mesial_buccal",
    "mesial",
    "mesial_occlusal",
    "distal_occlusal",
  ],
  LR7: [
    "mesial_lingual",
    "distal_lingual",
    "distal",
    "distal_buccal",
    "mesial_buccal",
    "mesial",
    "mesial_occlusal",
    "distal_occlusal",
  ],
  LR8: [
    "mesial_lingual",
    "distal_lingual",
    "distal",
    "distal_buccal",
    "mesial_buccal",
    "mesial",
    "mesial_occlusal",
    "distal_occlusal",
  ],
};

export const teethNumbersWithNoTreatment = {
  FissureSealant: [
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
  ],
  ImpactedToothCut: [
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12",
    "13",
    "14",
    "15",
    "18",
    "19",
    "20",
    "21",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30",
    "31",
  ],
  Sinus: [
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12",
    "17",
    "18",
    "19",
    "20",
    "21",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30",
    "31",
    "32",
  ],
  Sinus_Drop: [
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12",
    "17",
    "18",
    "19",
    "20",
    "21",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30",
    "31",
    "32",
  ],
  Sinus_Lift: [
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12",
    "17",
    "18",
    "19",
    "20",
    "21",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30",
    "31",
    "32",
  ],
  ZygomaticImplant: [
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12",
    "17",
    "18",
    "19",
    "20",
    "21",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30",
    "31",
    "32",
  ],
  ZygomaticImplantBridge: [
    "17",
    "18",
    "19",
    "20",
    "21",
    "22",
    "23",
    "24",
    "25",
    "26",
    "27",
    "28",
    "29",
    "30",
    "31",
    "32",
  ],
};

//correct naming how they gave us
// "DistalOcclusal": { "decaySeverity": 1, "fillingSize": 1 },
//         "Distal": { "decaySeverity": 1, "fillingSize": 1 },
//         "DistalBuccal": { "decaySeverity": 1, "fillingSize": 1 },
//         "DistalPalatal": { "decaySeverity": 1, "fillingSize": 1 },
//         "Mesial": { "decaySeverity": 1, "fillingSize": 1 },
//         "MesialBuccal": { "decaySeverity": 1, "fillingSize": 1 },
//         "MesialOcclusal": { "decaySeverity": 1, "fillingSize": 1 },
//         "MesialPalatal": { "decaySeverity": 1, "fillingSize": 1 }
