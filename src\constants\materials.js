import * as THREE from "three";

export const createSkullMaterial = () =>
  new THREE.MeshStandardMaterial({
    transparent: true,
    opacity: 0.35,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    emissiveIntensity: 0.15,
    depthWrite: false, // Important for transparency
    depthTest: true, // Keep depth testing
    roughness: 0.4,
    metalness: 0.1,
  });

export const createGumMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffb39f, // Pink color for gums
    transparent: true,
    opacity: 0.35,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    emissiveIntensity: 0.15,
    depthWrite: false, // Important for transparency
    depthTest: true, // Keep depth testing
    roughness: 0.4,
    metalness: 0.1,
  });

export const createDefaultMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffffff,
    metalness: 0.3,
    roughness: 0.2,
    transparent: false,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export const createHighlightMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffd700,
    metalness: 0.4,
    roughness: 0.2,
    transparent: false,
    emissive: 0xffe57f,
    emissiveIntensity: 0.6,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export const createMetalMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    color: 0x888888,
    metalness: 1,
    roughness: 0.3,
    // transmission: 0.0,
    reflectivity: 0.9,
    ior: 1.5,
    clearcoat: 0.0,
  });

export const createGlassMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    color: 0xffffff,
    metalness: 0.0,
    roughness: 0.0,
    // transmission: 1.0,
    reflectivity: 0.1,
    ior: 1.52,
    clearcoat: 0.0,
  });

export const createGoldMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    color: 0xffd700,
    metalness: 1.0,
    roughness: 0.2,
    // transmission: 0.0,
    reflectivity: 0.95,
    ior: 1.5,
    clearcoat: 0.0,
  });

export const createSilverMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    color: 0xc0c0c0,
    metalness: 1.0,
    roughness: 0.25,
    // transmission: 0.0,
    reflectivity: 0.95,
    ior: 1.5,
    clearcoat: 0.0,
  });

export const createTransparentMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(250 / 255, 250 / 255, 250 / 255),
    metalness: 0,
    roughness: 0.1,
    transparent: true,
    opacity: 0.5,
  });

export const createMatMaterial = (roughness) =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(240 / 255, 240 / 255, 240 / 255),
    metalness: 0,
    roughness,
  });

export const createBlackMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(0 / 255, 0 / 255, 0 / 255),
    metalness: 0,
    roughness: 1,
  });

export const createRoughWhiteMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: new THREE.Color(125 / 255, 125 / 255, 125 / 255),
    metalness: 0,
    roughness: 1,
  });

export const createEmaxMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    color: 0xf5f5f5,
    metalness: 0.0,
    roughness: 0.4,
    // transmission: 0.6,
    reflectivity: 0.3,
    ior: 1.47,
    clearcoat: 0.0,
  });

export const createPorcelainMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    color: 0xfaf0e6,
    metalness: 0.0,
    roughness: 0.5,
    // transmission: 0.4,
    reflectivity: 0.2,
    ior: 1.5,
    clearcoat: 0.0,
  });

export const createZirconiaMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    color: 0xeaeaea,
    metalness: 0.0,
    roughness: 0.3,
    // transmission: 0.2,
    reflectivity: 0.3,
    ior: 2.1,
    clearcoat: 0.0,
  });

export const createCompositeMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    color: 0xcccccc,
    metalness: 0.0,
    roughness: 0.6,
    // transmission: 0.1,
    reflectivity: 0.2,
    ior: 1.4,
    clearcoat: 0.0,
  });

export const createEmpressMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    name: "Empress",
    color: 0xf8f8ff,
    metalness: 0.0,
    roughness: 0.45,
    // transmission: 0.5,
    reflectivity: 0.25,
    ior: 1.48,
    clearcoat: 0.0,
  });

export const createCerecMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    color: 0xf0f0f0,
    metalness: 0.0,
    roughness: 0.4,
    // transmission: 0.5,
    reflectivity: 0.3,
    ior: 1.5,
    clearcoat: 0.0,
  });

export const createHybridMaterial = () =>
  new THREE.MeshPhysicalMaterial({
    color: 0xdcdcdc,
    metalness: 0.0,
    roughness: 0.55,
    // transmission: 0.3,
    reflectivity: 0.2,
    ior: 1.45,
    clearcoat: 0.0,
  });

export const BoneGraftingBlockMaterial = () => {
  const textureLoader = new THREE.TextureLoader();
  const diffuseMap = textureLoader.load(
    "/Materials/BoneGraftingBlock/Bone-Texture.jpg",
  );
  const normalMap = textureLoader.load(
    "/Materials/BoneGraftingBlock/Bone-Texture-Normal.jpg",
  );

  // Repeat the texture 5 times
  diffuseMap.wrapS = THREE.RepeatWrapping;
  diffuseMap.wrapT = THREE.RepeatWrapping;
  diffuseMap.repeat.set(7, 7);

  // Repeat the normal map 5 times too (important for consistency)
  normalMap.wrapS = THREE.RepeatWrapping;
  normalMap.wrapT = THREE.RepeatWrapping;
  normalMap.repeat.set(7, 7);

  return new THREE.MeshStandardMaterial({
    color: 0xd2d2d2,
    map: diffuseMap,
    normalMap: normalMap,
    roughness: 1,
    metalness: 0.2,
  });
};

export const materialCreators = {
  Skull: createSkullMaterial,
  Gum: createGumMaterial,
  Default: createDefaultMaterial,
  Highlight: createHighlightMaterial,
  Metal: createMetalMaterial,
  Glass: createGlassMaterial,
  Gold: createGoldMaterial,
  Silver: createSilverMaterial,
  Transparent: createTransparentMaterial,
  Mat: createMatMaterial,
  Black: createBlackMaterial,
  RoughWhite: createRoughWhiteMaterial,
  Emax: createEmaxMaterial,
  Porcelain: createPorcelainMaterial,
  Zirconia: createZirconiaMaterial,
  Composite: createCompositeMaterial,
  Empress: createEmpressMaterial,
  Cerec: createCerecMaterial,
  Hybrid: createHybridMaterial,
  BoneGraftingBlock: BoneGraftingBlockMaterial,
};
