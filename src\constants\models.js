import { BASE_URL, TREATMENTS_VERSION } from "../config/api";

// Treatments that have specific _A and _B folders.
// These should match the exact casing of the treatment name used in logic/passed as parameter.
const A_B_SPECIFIC_TREATMENTS = new Set([
  "Clasp",
  "Veneers",
  "BoneGraftingBlock",
  "FlexiDenture",
  "MarylandBridge",
  "MarylandWing",
  "UneruptedTooth",
  "ChromePartialDenture",
]);

// Treatments that require the default tooth model to be loaded alongside them.
export const DUAL_LOAD_TREATMENTS = new Set([
  // "Apictomy",
  // "Clasp",
  // "Crown",
  // "Bridge",
  // "Veneers",
  // "Pontic",
  // "Implant",
  // "Abutment",
  // "FlexiDenture",
  // "MarylandBridge",
  // "MarylandWing",
  // "BoneGraftingBlock",
  // "SinusDrop",
  // "UneruptedTooth",
  // "ImplantFullDenture",
  // "PartialDenture",
]);

export function getModelUrl(treatmentName, stateKey, patientType) {
  const s3versionForSpecificTreatments = TREATMENTS_VERSION; // e.g., "treatmentsV3"
  const s3versionForAdultGeneric = "treatmentsV4/states"; // For generic adult skull/jaw

  const stateViewSuffix = stateKey === "STATE_A" ? "A" : "B"; // For "StateA.glb", "ChildStateA.glb"

  // Use a logical name for default/child checks, but original treatmentName for paths
  const logicalName =
    treatmentName && typeof treatmentName === "string"
      ? treatmentName.charAt(0).toUpperCase() +
        treatmentName.slice(1).toLowerCase()
      : "Default";

  // Check for direct file references (e.g., for Inlay/Onlay tooth-specific models)
  // These names are expected to be pre-constructed like "Inlay_1_Base"
  const isDirectFileReference =
    treatmentName && /^([a-zA-Z]+)_(\d+C?)_([a-zA-Z_]+)$/.test(treatmentName);

  // CHILDREN
  if (patientType === "CHILDREN") {
    if (isDirectFileReference) {
      const match = treatmentName.match(/^([a-zA-Z]+)_(\d+C?)_([a-zA-Z_]+)$/);
      // match[1] should be "Inlay", "Onlay", etc.
      if (
        match &&
        match[1] &&
        (match[1].startsWith("Inlay") || match[1].startsWith("Onlay"))
      ) {
        const treatmentTypeFolder = "Onlay";
        return `${BASE_URL}/${s3versionForSpecificTreatments}/child/${treatmentTypeFolder}/${treatmentName}.glb`;
      }
      // Fallback for other direct references if any, or if parsing fails (should not happen for Inlay/Onlay)
      return `${BASE_URL}/${s3versionForSpecificTreatments}/child/${treatmentName}.glb`; // Original might have missed /child/
    }
    // Generic child model: if treatmentName is "child", "Child", "default", "Default", or null/undefined
    if (logicalName === "Child" || logicalName === "Default") {
      return `${BASE_URL}/${s3versionForSpecificTreatments}/child/ChildState${stateViewSuffix}.glb`;
    }

    // Specific treatments for CHILDREN (e.g., "Clasp" for a child)
    // Use the raw `treatmentName` for Set check and path construction, assuming it has correct casing.
    if (A_B_SPECIFIC_TREATMENTS.has(treatmentName)) {
      const folderAndFileName = `${treatmentName}_${stateViewSuffix}`;
      return `${BASE_URL}/${s3versionForSpecificTreatments}/${folderAndFileName}/${folderAndFileName}.glb`;
    } else {
      // Standard treatment: FolderName/FolderName.glb
      const modelTreatmentName =
        treatmentName === "Inlay" ? "Onlay" : treatmentName;
      return `${BASE_URL}/${s3versionForSpecificTreatments}/${modelTreatmentName}/${modelTreatmentName}.glb`;
    }
  }

  // ADULT
  if (patientType === "ADULT") {
    if (isDirectFileReference) {
      const match = treatmentName.match(/^([a-zA-Z]+)_(\d+C?)_([a-zA-Z_]+)$/);
      // match[1] should be "Inlay", "Onlay", etc.
      if (
        match &&
        match[1] &&
        (match[1].startsWith("Inlay") || match[1].startsWith("Onlay"))
      ) {
        const treatmentTypeFolder = "Onlay";
        return `${BASE_URL}/${s3versionForSpecificTreatments}/${treatmentTypeFolder}/${treatmentName}.glb`;
      }
      // Fallback for other direct references if any, or if parsing fails
      return `${BASE_URL}/${s3versionForSpecificTreatments}/${treatmentName}.glb`;
    }
    // Generic adult model: if treatmentName is "Default", "Skull", "Jaw", or null/undefined
    if (
      logicalName === "Default" ||
      logicalName === "Skull" ||
      logicalName === "Jaw"
    ) {
      return `${BASE_URL}/${s3versionForAdultGeneric}/states${stateViewSuffix}.glb`;
    }

    // Specific treatments for ADULTS
    // Use the raw `treatmentName` for Set check and path construction.
    if (A_B_SPECIFIC_TREATMENTS.has(treatmentName)) {
      const folderAndFileName = `${treatmentName}_${stateViewSuffix}`;
      return `${BASE_URL}/${s3versionForSpecificTreatments}/${folderAndFileName}/${folderAndFileName}.glb`;
    } else {
      // Standard treatment: FolderName/FolderName.glb
      const modelTreatmentName =
        treatmentName === "Inlay" ? "Onlay" : treatmentName;
      return `${BASE_URL}/${s3versionForSpecificTreatments}/${modelTreatmentName}/${modelTreatmentName}.glb`;
    }
  }

  return null;
}

export const MODELS = {
  // SKULL and JAW models are now dynamically loaded using getModelUrl.
  // Static models that don't fit the dynamic pattern remain here.
  CHARTING: `${BASE_URL}/states/ChartingV26.glb`,
  WATCHTOOL: `${BASE_URL}/states/ChartingWatchTool.glb`,
};
