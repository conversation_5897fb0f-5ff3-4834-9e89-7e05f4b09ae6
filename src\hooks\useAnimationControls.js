import { useState, useRef, useEffect } from "react";
import * as THREE from "three";
import { createPositionLockedMixer } from "../utils/modelUtils";

export const useAnimationControls = (name = "default") => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const frameCountRef = useRef(0);
  const mixersRef = useRef(new Map());
  const actionsRef = useRef(new Map());

  const playAnimations = () => {

    if (!isPaused) {
      frameCountRef.current = 0;
    }

    actionsRef.current.forEach((action) => {
      action.stop();
      action.reset();
    });

    actionsRef.current.forEach((action) => {
      action.play();
      action.setEffectiveWeight(1);
      action.paused = false;
    });

    setIsPlaying(true);
    setIsPaused(false);
  };

  const pauseAnimations = () => {
    actionsRef.current.forEach((action) => {
      action.paused = true;
    });
    setIsPaused(true);
  };

  const resetAnimations = () => {
    frameCountRef.current = 0;

    actionsRef.current.forEach((action) => {
      action.stop();
      action.reset();
    });

    setIsPlaying(false);
    setIsPaused(false);
  };

  const processAnimations = (objectId, animations, scene) => {

    if (!animations || animations.length === 0) {
      return;
    }

    const mixer = createPositionLockedMixer(scene);
    mixersRef.current.set(objectId, mixer);

    animations.forEach((clip) => {

      const validTracks = clip.tracks.filter((track) => {
        const nodeName = track.name.split(".")[0];
        const nodeExists = !!scene.getObjectByName(nodeName);
        return nodeExists;
      });


      if (validTracks.length === 0) {
        return;
      }

      const validClip = new THREE.AnimationClip(
        clip.name,
        clip.duration,
        validTracks,
      );

      const action = mixer.clipAction(validClip);
      action.setLoop(THREE.LoopOnce);
      action.clampWhenFinished = true;

      const actionKey = `${objectId}_${validClip.name}`;
      actionsRef.current.set(actionKey, action);
      
    });

  };

  const updateAnimations = (delta) => {
    if (isPlaying && !isPaused) {
      mixersRef.current.forEach((mixer) => mixer.update(delta));

      const fps = 30;
      frameCountRef.current += delta * fps;

      if (frameCountRef.current >= 30) {
        pauseAnimations();
      }
    }
  };

  const cleanupAnimations = () => {
    mixersRef.current.forEach((mixer) => mixer.stopAllAction());
    mixersRef.current.clear();
    actionsRef.current.clear();
    frameCountRef.current = 0;
    setIsPlaying(false);
    setIsPaused(false);
  };

  // Setup global animation controls
  useEffect(() => {
    const setupAnimationControls = () => {
      const controls = {
        play: playAnimations,
        pause: pauseAnimations,
        reset: resetAnimations,
        isPlaying: () => isPlaying,
        isPaused: () => isPaused,
        currentFrame: () => frameCountRef.current,
      };

      window[`${name}AnimationControls`] = controls;

      // Link with other animation controls if they exist
      if (name !== "jaw" && window.jawAnimationControls) {
        const originalJawPlay = window.jawAnimationControls.play;
        window.jawAnimationControls.play = () => {
          originalJawPlay();
          playAnimations();
        };

        window.jawAnimationControls._originalPlay = originalJawPlay;

        const originalJawReset = window.jawAnimationControls.reset;
        window.jawAnimationControls.reset = () => {
          originalJawReset();
          resetAnimations();
        };
        window.jawAnimationControls._originalReset = originalJawReset;
      }
    };

    setupAnimationControls();

    return () => {
      if (name !== "jaw" && window.jawAnimationControls) {
        if (window.jawAnimationControls._originalPlay) {
          window.jawAnimationControls.play =
            window.jawAnimationControls._originalPlay;
        }
        if (window.jawAnimationControls._originalReset) {
          window.jawAnimationControls.reset =
            window.jawAnimationControls._originalReset;
        }
      }

      // Remove global reference
      delete window[`${name}AnimationControls`];
    };
  }, [name, isPlaying, isPaused]);

  return {
    isPlaying,
    isPaused,
    frameCountRef,
    mixersRef,
    actionsRef,
    playAnimations,
    pauseAnimations,
    resetAnimations,
    processAnimations,
    updateAnimations,
    cleanupAnimations,
  };
};
