import * as THREE from "three";
import { MathUtils } from "three";

// Cache for models and animations
export const modelCache = new Map();
export const animationCache = new Map();
export const materialCaches = {
  jaw: new Map(),
  skull: new Map(),
  charting: new Map(),
  single_treatment: new Map(), // Add cache for single_treatment view
};

// Process animation tracks to prevent position changes
export const sanitizeAnimationTracks = (originalClip) => {
  if (!originalClip) return null;

  // Clone the clip to avoid modifying the original
  const clip = originalClip.clone();
  clip.userData = clip.userData || {};
  // This flag indicates if the *original* clip had position tracks.
  clip.userData.hasPositionTracks = originalClip.tracks.some((track) =>
    track.name.endsWith(".position"),
  );

  const newTracks = [];

  originalClip.tracks.forEach((originalTrack) => {
    // Clone the track to modify its values if necessary
    const track = originalTrack.clone();

    if (track.name.endsWith(".position")) {
      // Log the original position values

      // Ensure it's a VectorKeyframeTrack and has values
      if (track.values && track.values.length >= 3) {
        const initialX = track.values[0];
        const initialZ = track.values[2];

        // Lock X and Z to initial values
        for (let i = 0; i < track.values.length / 3; i++) {
          if (track.values[i * 3] !== initialX) {
            track.values[i * 3] = initialX; // Set X to initial X
          }
          // track.values[i * 3 + 1] remains the animated Y
          if (track.values[i * 3 + 2] !== initialZ) {
            track.values[i * 3 + 2] = initialZ; // Set Z to initial Z
          }
        }
      }
      newTracks.push(track);
    } else {
      // Keep non-position tracks as-is
      newTracks.push(track);
    }
  });

  clip.tracks = newTracks; // Assign the (potentially) modified tracks

  // Return the modified clip
  return clip;
};

// Alternative approach: Create a position-locking animation mixer wrapper
export const createPositionLockedMixer = (root) => {
  const mixer = new THREE.AnimationMixer(root);
  const originalUpdate = mixer.update.bind(mixer);

  // Store initial positions
  const initialPositions = new Map();
  root.traverse((obj) => {
    if (obj.isObject3D) {
      initialPositions.set(obj.uuid, {
        x: obj.position.x,
        z: obj.position.z,
      });
    }
  });

  // Override update to lock X/Z positions
  mixer.update = function (deltaTime) {
    // Call original update
    const result = originalUpdate(deltaTime);

    // Restore X/Z positions
    root.traverse((obj) => {
      if (obj.isObject3D && initialPositions.has(obj.uuid)) {
        const initial = initialPositions.get(obj.uuid);
        obj.position.x = initial.x;
        obj.position.z = initial.z;
      }
    });

    return result;
  };

  return mixer;
};

// List of treatments that have state-specific models (A for skull, B for jaw)
const stateSpecificTreatments = [
  "ZygomaticImplant",
  "SinusDrop",
  "MarylandBridge",
  "MarylandWing",
  "ImplantFullDenture",
  "FlexiDenture",
  "PartialDenture",
  "ChromePartialDenture",
  "Clasp",
  "State",
  "BoneGraftingBlock",
  "UneruptedTooth",
  "Veneers",
];

// Check if a treatment has state-specific models
export const checkForStateSpecificModel = (treatmentName) => {
  return stateSpecificTreatments.some(
    (prefix) =>
      treatmentName === prefix || treatmentName.startsWith(`${prefix}_`),
  );
};

// Get model parts based on treatment names
import { DUAL_LOAD_TREATMENTS } from "../constants/models";

export const getModelParts = (toothData) => {
  // If no treatments array or it's empty, return default
  if (!toothData.treatments || toothData.treatments.length === 0) {
    return ["Default"];
  }

  // Get all unique treatment names
  const treatmentNames = [...new Set(toothData.treatments.map((t) => t.name))];

  // If there are no treatment names, return default
  if (treatmentNames.length === 0) {
    return ["Default"];
  }

  // Initialize parts array
  let parts = [];
  let requiresDefaultLoad = false;

  // Check if any treatment requires dual loading or has need_default_tooth flag
  for (const treatment of toothData.treatments) {
    if (
      DUAL_LOAD_TREATMENTS.has(treatment.name) ||
      treatment.need_default_tooth
    ) {
      requiresDefaultLoad = true;
      break;
    }
  }

  // If dual loading is required or the default_tooth flag is set, add "Default"
  if (requiresDefaultLoad || toothData.default_tooth) {
    parts.push("Default");
  }

  // Process each treatment name
  treatmentNames.forEach((treatmentName) => {
    // Special case for Filling which needs both Decay and Filling parts
    if (treatmentName === "Filling") {
      if (!parts.includes("Decay")) parts.push("Decay");
      if (!parts.includes("Filling")) parts.push("Filling");
    } else {
      // For state-specific treatments, remove any state suffix
      const basePartName =
        stateSpecificTreatments.find((prefix) =>
          treatmentName.startsWith(prefix),
        ) || treatmentName;

      // Add the treatment name if it's not already in the parts array
      if (!parts.includes(basePartName)) {
        parts.push(basePartName);
      }
    }
  });

  // If no parts were added, it means no valid treatments were found,
  // so we should load the default tooth.
  if (parts.length === 0) {
    return ["Default"];
  }

  return [...new Set(parts)]; // Return unique parts
};

// Get the correct model path based on patient type
import { BASE_URL, TREATMENTS_VERSION } from "../config/api";
// DUAL_LOAD_TREATMENTS is already imported at the top

export const getModelPathForPatientType = (
  number,
  treatmentName,
  patientType = "ADULT",
) => {
  // If patient type is CHILDREN, use the child directory
  if (patientType === "CHILDREN") {
    return `${BASE_URL}/${TREATMENTS_VERSION}/child/${treatmentName}/${number}C.glb`;
  }

  // Otherwise use the default path
  return treatmentName === "Default"
    ? `${BASE_URL}/${TREATMENTS_VERSION}/${treatmentName}/${number}.glb`
    : `${BASE_URL}/${TREATMENTS_VERSION}/${treatmentName}/${number}_${treatmentName}.glb`;
};

// Clone a model with proper material handling
export const cloneModel = (source, currentView = "jaw") => {
  if (!source) return null;

  const clone = source.clone(true);
  const materialCache = materialCaches[currentView];

  // Make sure materialCache exists
  if (!materialCache) {
    return clone;
  }

  clone.traverse((obj) => {
    if (obj.isMesh) {
      obj.uuid = MathUtils.generateUUID();

      const originalMaterialId = obj.material.uuid;
      let clonedMaterial;

      if (materialCache.has(originalMaterialId)) {
        clonedMaterial = materialCache.get(originalMaterialId);
      } else {
        clonedMaterial = obj.material.clone();
        clonedMaterial.uuid = MathUtils.generateUUID();
        materialCache.set(originalMaterialId, clonedMaterial);
      }

      obj.material = clonedMaterial;
      obj.geometry = obj.geometry.clone();
      obj.geometry.uuid = MathUtils.generateUUID();
    }
  });
  return clone;
};

// Find exact morph target for a surface
// Find exact morph target for a surface
export const findExactSurfaceMorphTarget = (morphTargetNames, surfaceName) => {
  const lowerSurface = surfaceName.toLowerCase();

  // Prioritize morph targets where the last segment of the name contains the surface name.
  // This handles compound names like "DistalBuccal" and simple names like "Distal".
  // e.g., Morpher_ncl1_56.OnlayTarget_2_DistalBuccal -> last segment is "distalbuccal"
  const exactMatch = morphTargetNames.find((name) => {
    const lowerName = name.toLowerCase();
    const parts = lowerName.split(/[._]/);
    const lastPart = parts[parts.length - 1];

    // Check if the last segment of the morph target name contains the surface name.
    // This is more robust than a simple .endsWith() or a general .includes().
    if (lastPart.includes(lowerSurface)) {
      return true;
    }
    return false;
  });

  if (exactMatch) {
    return exactMatch;
  }

  // Fallback for backward compatibility, though the new logic should cover most cases.
  return morphTargetNames.find((name) =>
    name.toLowerCase().includes(lowerSurface),
  );
};

// Create standard materials
export const createDefaultMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffffff,
    metalness: 0.3,
    roughness: 0.2,
    transparent: false,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export const createHighlightMaterial = (originalMaterial = null) => {
  // Build debug props object, filtering out undefined values
  const debugProps = {};
  if (originalMaterial) {
    const props = [
      "transparent",
      "opacity",
      "renderOrder",
      "depthWrite",
      "depthTest",
      "blending",
      "polygonOffset",
    ];
    props.forEach((prop) => {
      if (originalMaterial[prop] !== undefined) {
        debugProps[prop] = originalMaterial[prop];
      }
    });
  }

  // Start with base highlight properties
  const highlightProps = {
    color: 0xffd700,
    metalness: 0.4,
    roughness: 0.2,
    emissive: 0xffe57f,
    emissiveIntensity: 0.6,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
  };

  // If we have an original material, preserve its transparency properties
  if (originalMaterial) {
    // Always copy these core properties
    highlightProps.transparent = originalMaterial.transparent;
    highlightProps.opacity = originalMaterial.opacity;
    highlightProps.depthWrite = originalMaterial.depthWrite;
    highlightProps.polygonOffset = originalMaterial.polygonOffset;
    highlightProps.polygonOffsetFactor = originalMaterial.polygonOffsetFactor;
    highlightProps.polygonOffsetUnits = originalMaterial.polygonOffsetUnits;

    // Only copy these properties if they are defined to avoid warnings
    if (originalMaterial.renderOrder !== undefined) {
      highlightProps.renderOrder = originalMaterial.renderOrder;
    }
    if (originalMaterial.blending !== undefined) {
      highlightProps.blending = originalMaterial.blending;
    }
    if (originalMaterial.depthTest !== undefined) {
      highlightProps.depthTest = originalMaterial.depthTest;
    }
    if (originalMaterial.alphaTest !== undefined) {
      highlightProps.alphaTest = originalMaterial.alphaTest;
    }
    // Preserve side property if it exists
    if (originalMaterial.side !== undefined) {
      highlightProps.side = originalMaterial.side;
    }
  } else {
    // Default non-transparent properties
    highlightProps.transparent = false;
    highlightProps.depthWrite = true;
  }

  // Filter out undefined values from highlightProps to prevent warnings
  const cleanedProps = {};
  Object.keys(highlightProps).forEach((key) => {
    if (highlightProps[key] !== undefined) {
      cleanedProps[key] = highlightProps[key];
    }
  });

  const material = new THREE.MeshStandardMaterial(cleanedProps);

  return material;
};

export const createSurfaceHighlightMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0x008080,
    metalness: 0.2,
    roughness: 0.8,
    transparent: true,
    opacity: 0.5,
    side: THREE.DoubleSide,
    depthWrite: true,
  });

export const createSkullMaterial = () =>
  new THREE.MeshStandardMaterial({
    transparent: true,
    opacity: 0.35,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    emissiveIntensity: 0.15,
    depthWrite: false, // Important for transparency
    depthTest: true, // Keep depth testing
    roughness: 0.4,
    metalness: 0.1,
  });

export const createGumMaterial = () =>
  new THREE.MeshStandardMaterial({
    color: 0xffb39f, // Pink color for gums
    transparent: true,
    opacity: 0.35,
    envMapIntensity: 0.2,
    side: THREE.DoubleSide,
    emissiveIntensity: 0.15,
    depthWrite: false, // Important for transparency
    depthTest: true, // Keep depth testing
    roughness: 0.4,
    metalness: 0.1,
  });

// Force a scene update
export const forceSceneUpdate = (renderer, scene, camera) => {
  if (!renderer || !scene || !camera) {
    return;
  }

  // Dispose render lists to force a full rebuild
  renderer.renderLists.dispose();

  // Render the scene
  renderer.render(scene, camera);
};

// Clear all teeth from the scene
export const clearAllTeethFromScene = (scene) => {
  if (!scene) {
    return;
  }

  // Find all teeth objects in the scene
  const teethObjects = [];
  const teethMeshes = [];
  const decayGroups = [];

  // First pass: identify all objects that might be teeth
  scene.traverse((object) => {
    // Check for objects with tooth-related names or userData
    const name = object.name.toLowerCase();
    const isTooth =
      (object.userData &&
        (object.userData.type === "tooth" || object.userData.isTooth)) ||
      name.includes("tooth") ||
      name.includes("teeth") ||
      name.includes("molar") ||
      name.includes("incisor") ||
      name.includes("canine") ||
      name.includes("premolar") ||
      name.includes("filling");

    // Check specifically for decay groups
    const isDecayGroup = name.includes("decay") || name.includes("caries");

    if (isTooth) {
      // Only include direct tooth objects, not their children
      if (
        !object.parent ||
        !object.parent.userData ||
        (object.parent.userData.type !== "tooth" &&
          !object.parent.userData.isTooth)
      ) {
        teethObjects.push(object);
      }
    }

    // Collect decay groups separately
    if (isDecayGroup) {
      decayGroups.push(object);
    }

    // Also collect all meshes with morph targets (likely teeth)
    if (
      object.isMesh &&
      object.morphTargetInfluences &&
      object.morphTargetInfluences.length > 0
    ) {
      teethMeshes.push(object);
    }
  });

  if (
    teethObjects.length === 0 &&
    teethMeshes.length === 0 &&
    decayGroups.length === 0
  ) {
    return;
  }

  // Remove all teeth objects from the scene
  teethObjects.forEach((object) => {
    if (object.parent) {
      object.parent.remove(object);
    }

    // Dispose resources
    object.traverse((child) => {
      if (child.isMesh) {
        // Clear morph targets
        if (child.morphTargetInfluences) {
          child.morphTargetInfluences.fill(0);
        }

        // Dispose geometry
        if (child.geometry) {
          child.geometry.dispose();
        }

        // Dispose materials
        if (Array.isArray(child.material)) {
          child.material.forEach((m) => m?.dispose());
        } else if (child.material) {
          child.material.dispose();
        }

        // Ensure the mesh is not visible
        child.visible = false;
      }
    });

    // Clear any references
    object.clear();
  });

  // Handle decay groups
  decayGroups.forEach((group) => {
    if (group.parent) {
      group.parent.remove(group);
    }

    // Dispose resources
    group.traverse((child) => {
      if (child.isMesh) {
        // Clear morph targets
        if (child.morphTargetInfluences) {
          child.morphTargetInfluences.fill(0);
        }

        // Dispose geometry
        if (child.geometry) {
          child.geometry.dispose();
        }

        // Dispose materials
        if (Array.isArray(child.material)) {
          child.material.forEach((m) => m?.dispose());
        } else if (child.material) {
          child.material.dispose();
        }

        // Ensure the mesh is not visible
        child.visible = false;
      }
    });

    // Clear any references
    group.clear();
  });

  // Handle any remaining meshes with morph targets
  teethMeshes.forEach((mesh) => {
    if (!mesh.parent) return; // Skip if already removed

    // Reset morph targets
    if (mesh.morphTargetInfluences) {
      mesh.morphTargetInfluences.fill(0);
    }

    // Make invisible
    mesh.visible = false;
  });

  // Special handling for position objects with teeth
  const positionObjects = [];
  scene.traverse((object) => {
    if (object.name.includes("_Pos")) {
      // Check if this position object has any children
      let hasTeethChildren = false;
      object.traverse((child) => {
        if (
          child !== object &&
          (child.name.includes("tooth") || child.name.includes("Caries"))
        ) {
          hasTeethChildren = true;
        }
      });

      if (hasTeethChildren) {
        positionObjects.push(object);
      }
    }
  });

  // Remove all children from position objects
  positionObjects.forEach((posObj) => {
    while (posObj.children.length > 0) {
      const child = posObj.children[0];
      posObj.remove(child);

      // Dispose resources
      if (child.isMesh) {
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((m) => m?.dispose());
          } else {
            child.material.dispose();
          }
        }
      }
    }
  });
};
